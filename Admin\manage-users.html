<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Users - Shans Admin</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f7fe;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 10px;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        h1 {
            color: #2c3e50;
            margin: 0;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            min-height: 44px; /* Minimum touch target size */
            box-sizing: border-box;
            text-align: center;
        }

        .btn-primary {
            background-color: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background-color: #2980b9;
        }

        .btn-success {
            background-color: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background-color: #229954;
        }

        .btn-danger {
            background-color: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background-color: #c0392b;
        }

        .btn-secondary {
            background-color: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #7f8c8d;
        }

        .users-section {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .add-user-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border: 2px dashed #dee2e6;
        }

        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            align-items: end;
        }

        .form-group {
            flex: 1;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }

        input[type="email"],
        input[type="password"],
        select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }

        input[type="email"]:focus,
        input[type="password"]:focus,
        select:focus {
            outline: none;
            border-color: #3498db;
        }

        .users-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .table-container {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        .users-table th,
        .users-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .users-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #555;
        }

        .users-table tr:hover {
            background-color: #f8f9fa;
        }

        .admin-badge {
            background-color: #e74c3c;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .user-badge {
            background-color: #3498db;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .actions {
            display: flex;
            gap: 5px;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .message {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }

        .message.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        /* Confirmation Toast Styles */
        .confirmation-toast {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            padding: 24px;
            z-index: 10000;
            max-width: 400px;
            width: 90%;
            text-align: center;
            border: 1px solid #e0e0e0;
        }

        .confirmation-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 9999;
        }

        .confirmation-message {
            font-size: 1.1rem;
            color: #333;
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .confirmation-buttons {
            display: flex;
            gap: 12px;
            justify-content: center;
        }

        .confirm-btn, .cancel-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 80px;
        }

        .confirm-btn {
            background-color: #dc3545;
            color: white;
        }

        .confirm-btn:hover {
            background-color: #c82333;
        }

        .cancel-btn {
            background-color: #6c757d;
            color: white;
        }

        .cancel-btn:hover {
            background-color: #5a6268;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
        }

        /* Tablet styles */
        @media (max-width: 1024px) {
            .container {
                padding: 0 15px;
            }

            .header {
                padding: 15px;
            }

            .users-section {
                padding: 20px;
            }
        }

        /* Mobile styles */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .container {
                padding: 0;
            }

            .header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
                padding: 15px;
            }

            .header h1 {
                font-size: 24px;
                margin-bottom: 10px;
            }

            .header div {
                display: flex;
                flex-direction: column;
                gap: 10px;
                width: 100%;
            }

            .header .btn {
                width: 100%;
                text-align: center;
            }

            .form-row {
                flex-direction: column;
                gap: 10px;
            }

            .form-group {
                width: 100%;
            }

            .add-user-form {
                padding: 15px;
            }

            /* Table responsiveness */
            .users-table {
                font-size: 14px;
                display: block;
                overflow-x: auto;
                white-space: nowrap;
                border: 1px solid #ddd;
                border-radius: 5px;
            }

            .users-table thead,
            .users-table tbody,
            .users-table th,
            .users-table td,
            .users-table tr {
                display: block;
            }

            .users-table thead tr {
                position: absolute;
                top: -9999px;
                left: -9999px;
            }

            .users-table tr {
                border: 1px solid #ccc;
                margin-bottom: 10px;
                padding: 10px;
                border-radius: 5px;
                background: #f9f9f9;
                position: relative;
                white-space: normal;
            }

            .users-table td {
                border: none;
                position: relative;
                padding: 8px 8px 8px 35%;
                text-align: left;
                white-space: normal;
            }

            .users-table td:before {
                content: attr(data-label) ": ";
                position: absolute;
                left: 6px;
                width: 30%;
                padding-right: 10px;
                white-space: nowrap;
                font-weight: bold;
                color: #555;
            }

            .actions {
                flex-direction: row;
                flex-wrap: wrap;
                gap: 5px;
                justify-content: flex-start;
            }

            .actions .btn {
                padding: 8px 12px;
                font-size: 12px;
                min-width: 60px;
                flex: 1;
                text-align: center;
            }

            /* Modal improvements */
            #editModal > div,
            #passwordModal > div {
                width: 95%;
                max-width: none;
                margin: 20px auto;
                max-height: 90vh;
                overflow-y: auto;
            }

            .users-section h2 {
                font-size: 20px;
            }
        }

        /* Small mobile styles */
        @media (max-width: 480px) {
            .header h1 {
                font-size: 20px;
            }

            .btn {
                padding: 12px 16px;
                font-size: 14px;
            }

            .users-table {
                font-size: 13px;
            }

            .users-table td {
                padding: 6px 6px 6px 35%;
            }

            .actions {
                flex-direction: column;
                gap: 8px;
            }

            .actions .btn {
                width: 100%;
                padding: 10px;
                font-size: 13px;
            }

            .add-user-form {
                padding: 10px;
            }

            input[type="email"],
            input[type="password"],
            select {
                padding: 12px;
                font-size: 16px; /* Prevents zoom on iOS */
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Manage Users</h1>
            <div>
                <a href="index.html" class="btn btn-secondary">Back to Dashboard</a>
                <button onclick="logout()" class="btn btn-danger">Logout</button>
            </div>
        </div>

        <div class="message" id="message"></div>

        <div class="users-section">
            <h2>Add New User</h2>
            <div class="add-user-form">
                <form id="addUserForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="email">Email Address</label>
                            <input type="email" id="email" name="email" required>
                        </div>
                        <div class="form-group">
                            <label for="password">Password</label>
                            <input type="password" id="password" name="password" required>
                        </div>
                        <div class="form-group">
                            <div class="checkbox-group">
                                <input type="checkbox" id="is_admin" name="is_admin">
                                <label for="is_admin">Admin User</label>
                            </div>
                        </div>
                        <div class="form-group">
                            <button type="submit" class="btn btn-success">Add User</button>
                        </div>
                    </div>
                </form>
            </div>

            <h2>Existing Users</h2>
            <div id="usersLoading" class="loading">
                <div class="spinner"></div>
                <p>Loading users...</p>
            </div>

            <div id="usersContainer" style="display: none;">
                <div class="table-container">
                    <table class="users-table">
                        <thead>
                            <tr>
                                <th>Email</th>
                                <th>Role</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="usersTableBody">
                            <!-- Users will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit User Modal -->
    <div id="editModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 10px; width: 90%; max-width: 400px;">
            <h3>Edit User</h3>
            <form id="editUserForm">
                <input type="hidden" id="editUserId">
                <div class="form-group">
                    <label for="editEmail">Email Address</label>
                    <input type="email" id="editEmail" name="email" required>
                </div>
                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="editIsAdmin" name="is_admin">
                        <label for="editIsAdmin">Admin User</label>
                    </div>
                </div>
                <div style="display: flex; gap: 10px; margin-top: 20px;">
                    <button type="submit" class="btn btn-success">Update User</button>
                    <button type="button" onclick="closeEditModal()" class="btn btn-secondary">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Change Password Modal -->
    <div id="passwordModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 10px; width: 90%; max-width: 400px;">
            <h3>Change Password</h3>
            <form id="changePasswordForm">
                <input type="hidden" id="passwordUserId">
                <div class="form-group">
                    <label for="newPassword">New Password</label>
                    <input type="password" id="newPassword" name="password" required>
                </div>
                <div style="display: flex; gap: 10px; margin-top: 20px;">
                    <button type="submit" class="btn btn-success">Change Password</button>
                    <button type="button" onclick="closePasswordModal()" class="btn btn-secondary">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Auto-detect environment and set appropriate API URL
        const API_BASE_URL = (() => {
          const hostname = window.location.hostname;

          // Production environment (Netlify)
          if (hostname === 'shans-system.netlify.app') {
            return 'https://shans-backend.onrender.com/api';
          }

          // Local development
          if (hostname === 'localhost' || hostname === '127.0.0.1') {
            return 'https://shans-backend.onrender.com/api';
          }

          // Default fallback to production
          return 'https://shans-backend.onrender.com/api';
        })();
        let users = [];

        // Check authentication status
        async function checkAuthStatus() {
            const token = localStorage.getItem('authToken');
            const userInfo = localStorage.getItem('userInfo');

            if (!token || !userInfo) {
                window.location.href = '../login.html';
                return false;
            }

            const user = JSON.parse(userInfo);
            if (!user.is_admin) {
                localStorage.removeItem('authToken');
                localStorage.removeItem('userInfo');
                window.location.href = '../login.html';
                return false;
            }

            try {
                // Verify token with backend
                const response = await fetch(`${API_BASE_URL}/auth/me`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (!response.ok) {
                    localStorage.removeItem('authToken');
                    localStorage.removeItem('userInfo');
                    window.location.href = '../login.html';
                    return false;
                }

                const data = await response.json();
                if (!data.user.is_admin) {
                    localStorage.removeItem('authToken');
                    localStorage.removeItem('userInfo');
                    window.location.href = '../login.html';
                    return false;
                }

                return true;
            } catch (error) {
                console.error('Auth check failed:', error);
                localStorage.removeItem('authToken');
                localStorage.removeItem('userInfo');
                window.location.href = '../login.html';
                return false;
            }
        }

        // Logout functionality
        function logout() {
            fetch(`${API_BASE_URL}/auth/logout`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                }
            })
            .then(() => {
                localStorage.removeItem('authToken');
                localStorage.removeItem('userInfo');
                window.location.href = 'login.html';
            })
            .catch(error => {
                console.error('Logout error:', error);
                localStorage.removeItem('authToken');
                localStorage.removeItem('userInfo');
                window.location.href = 'login.html';
            });
        }

        // Show message
        function showMessage(text, type = 'success') {
            const messageEl = document.getElementById('message');
            messageEl.textContent = text;
            messageEl.className = `message ${type}`;
            messageEl.style.display = 'block';

            setTimeout(() => {
                messageEl.style.display = 'none';
            }, 5000);
        }

        // Confirmation toast system for Android WebView compatibility
        function showConfirmationToast(message, onConfirm, onCancel = null) {
            // Create overlay
            const overlay = document.createElement('div');
            overlay.className = 'confirmation-overlay';

            // Create toast
            const toast = document.createElement('div');
            toast.className = 'confirmation-toast';

            // Create message
            const messageDiv = document.createElement('div');
            messageDiv.className = 'confirmation-message';
            messageDiv.textContent = message;

            // Create buttons container
            const buttonsDiv = document.createElement('div');
            buttonsDiv.className = 'confirmation-buttons';

            // Create confirm button
            const confirmBtn = document.createElement('button');
            confirmBtn.className = 'confirm-btn';
            confirmBtn.textContent = 'Yes';
            confirmBtn.onclick = () => {
                document.body.removeChild(overlay);
                if (onConfirm) onConfirm();
            };

            // Create cancel button
            const cancelBtn = document.createElement('button');
            cancelBtn.className = 'cancel-btn';
            cancelBtn.textContent = 'Cancel';
            cancelBtn.onclick = () => {
                document.body.removeChild(overlay);
                if (onCancel) onCancel();
            };

            // Assemble the toast
            buttonsDiv.appendChild(confirmBtn);
            buttonsDiv.appendChild(cancelBtn);
            toast.appendChild(messageDiv);
            toast.appendChild(buttonsDiv);
            overlay.appendChild(toast);

            // Add to page
            document.body.appendChild(overlay);

            // Close on overlay click
            overlay.onclick = (e) => {
                if (e.target === overlay) {
                    document.body.removeChild(overlay);
                    if (onCancel) onCancel();
                }
            };
        }

        // Fetch users
        async function fetchUsers() {
            try {
                const response = await fetch(`${API_BASE_URL}/users`, {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                    }
                });

                if (!response.ok) {
                    throw new Error('Failed to fetch users');
                }

                users = await response.json();
                displayUsers();
            } catch (error) {
                console.error('Error fetching users:', error);
                showMessage('Failed to load users', 'error');
            } finally {
                document.getElementById('usersLoading').style.display = 'none';
                document.getElementById('usersContainer').style.display = 'block';
            }
        }

        // Display users in table
        function displayUsers() {
            const tbody = document.getElementById('usersTableBody');
            tbody.innerHTML = '';

            users.forEach(user => {
                const row = document.createElement('tr');
                const createdDate = new Date(user.created_at).toLocaleDateString();

                row.innerHTML = `
                    <td data-label="Email">${user.email}</td>
                    <td data-label="Role">
                        ${user.is_admin ?
                            '<span class="admin-badge">Admin</span>' :
                            '<span class="user-badge">User</span>'
                        }
                    </td>
                    <td data-label="Created">${createdDate}</td>
                    <td data-label="Actions">
                        <div class="actions">
                            <button onclick="editUser(${user.id})" class="btn btn-primary">Edit</button>
                            <button onclick="changePassword(${user.id})" class="btn btn-secondary">Password</button>
                            <button onclick="deleteUser(${user.id})" class="btn btn-danger">Delete</button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // Add user
        document.getElementById('addUserForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const formData = new FormData(e.target);
            const userData = {
                email: formData.get('email'),
                password: formData.get('password'),
                is_admin: formData.get('is_admin') === 'on'
            };

            try {
                const response = await fetch(`${API_BASE_URL}/users`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                    },
                    body: JSON.stringify(userData)
                });

                const data = await response.json();

                if (response.ok) {
                    showMessage('User added successfully');
                    e.target.reset();
                    fetchUsers();
                } else {
                    showMessage(data.message || 'Failed to add user', 'error');
                }
            } catch (error) {
                console.error('Error adding user:', error);
                showMessage('Network error. Please try again.', 'error');
            }
        });

        // Edit user
        function editUser(userId) {
            const user = users.find(u => u.id === userId);
            if (!user) return;

            document.getElementById('editUserId').value = user.id;
            document.getElementById('editEmail').value = user.email;
            document.getElementById('editIsAdmin').checked = user.is_admin;
            document.getElementById('editModal').style.display = 'block';
        }

        function closeEditModal() {
            document.getElementById('editModal').style.display = 'none';
        }

        document.getElementById('editUserForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const userId = document.getElementById('editUserId').value;
            const formData = new FormData(e.target);
            const userData = {
                email: formData.get('email'),
                is_admin: formData.get('is_admin') === 'on'
            };

            try {
                const response = await fetch(`${API_BASE_URL}/users/${userId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                    },
                    body: JSON.stringify(userData)
                });

                const data = await response.json();

                if (response.ok) {
                    showMessage('User updated successfully');
                    closeEditModal();
                    fetchUsers();
                } else {
                    showMessage(data.message || 'Failed to update user', 'error');
                }
            } catch (error) {
                console.error('Error updating user:', error);
                showMessage('Network error. Please try again.', 'error');
            }
        });

        // Change password
        function changePassword(userId) {
            document.getElementById('passwordUserId').value = userId;
            document.getElementById('passwordModal').style.display = 'block';
        }

        function closePasswordModal() {
            document.getElementById('passwordModal').style.display = 'none';
            document.getElementById('changePasswordForm').reset();
        }

        document.getElementById('changePasswordForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const userId = document.getElementById('passwordUserId').value;
            const formData = new FormData(e.target);
            const passwordData = {
                password: formData.get('password')
            };

            try {
                const response = await fetch(`${API_BASE_URL}/users/${userId}/password`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                    },
                    body: JSON.stringify(passwordData)
                });

                const data = await response.json();

                if (response.ok) {
                    showMessage('Password updated successfully');
                    closePasswordModal();
                } else {
                    showMessage(data.message || 'Failed to update password', 'error');
                }
            } catch (error) {
                console.error('Error updating password:', error);
                showMessage('Network error. Please try again.', 'error');
            }
        });

        // Delete user
        function deleteUser(userId) {
            const user = users.find(u => u.id === userId);
            if (!user) return;

            // Show confirmation toast instead of browser confirm
            showConfirmationToast(
                `Are you sure you want to delete user "${user.email}"? This action cannot be undone.`,
                () => {
                    // User confirmed - proceed with deletion
                    fetch(`${API_BASE_URL}/users/${userId}`, {
                        method: 'DELETE',
                        headers: {
                            'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.message === 'User deleted successfully') {
                            showMessage('User deleted successfully');
                            fetchUsers();
                        } else {
                            showMessage(data.message || 'Failed to delete user', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error deleting user:', error);
                        showMessage('Network error. Please try again.', 'error');
                    });
                },
                () => {
                    // User cancelled - do nothing
                    console.log('Delete cancelled by user');
                }
            );
        }

        // Initialize page
        window.onload = async function() {
            const isAuthenticated = await checkAuthStatus();
            if (!isAuthenticated) {
                return;
            }
            fetchUsers();
        };
    </script>
</body>
</html>