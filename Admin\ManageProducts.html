<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Shans Inventory System - Manage Products</title>
    <link rel="stylesheet" href="styles.css">
    <!-- Add within the <head> section -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/choices.js/public/assets/styles/choices.min.css">
    <script src="https://cdn.jsdelivr.net/npm/choices.js/public/assets/scripts/choices.min.js"></script>

    <style>
        /* Existing styles from your code... */

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Action buttons container */
        .action-buttons-container {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        /* Unified button style for all action buttons */
        .action-btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 15px;
            height: 40px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.3s, transform 0.2s;
            text-align: center;
            min-width: 120px;
        }

        /* Add margin to all buttons except the first one */
        .action-btn:not(:first-of-type) {
            margin-left: 10px;
        }

        .action-btn:hover {
            background-color: #0056b3;
            transform: scale(1.05);
        }

        /* Button icon styling */
        .button-icon, .plus-icon {
            margin-right: 5px;
            font-size: 16px;
            display: inline-flex;
            align-items: center;
        }

        /* Legacy class names for backward compatibility */
        .category-btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 15px;
            height: 40px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.3s, transform 0.2s;
            text-align: center;
        }

        .category-btn:hover {
            background-color: #0056b3;
            transform: scale(1.05);
        }

        .add-product-block {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 15px;
            height: 40px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.3s, transform 0.2s;
            text-align: center;
        }

        .add-product-block:hover {
            background-color: #0056b3;
            transform: scale(1.05);
        }

        .products-section {
            overflow-x: auto;
            max-width: 100%;
            margin-bottom: 20px;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        /* Search Bar */
        .search-bar {
            margin-bottom: 15px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
        }

        .search-bar input[type="text"] {
            width: 100%;
            max-width: 300px;
            padding: 8px 12px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }

        .search-bar input[type="text"]:focus {
            border-color: #007bff;
            outline: none;
        }

        /* Excel-like table styling */
        .products-section {
            overflow-x: auto;
            max-width: 100%;
            margin-bottom: 20px;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        /* Excel-style table class for all tables */
        .excel-style-table {
            width: 100%;
            border-collapse: collapse;
            background-color: white;
            font-size: 14px;
            border: 1px solid #e0e0e0;
            margin-bottom: 0;
        }

        .excel-style-table th, .excel-style-table td {
            text-align: left;
            padding: 8px 10px;
            border: 1px solid #e0e0e0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 200px;
        }

        .excel-style-table th {
            position: sticky;
            top: 0;
            background-color: #f2f2f2;
            color: #333;
            font-weight: bold;
            border-bottom: 2px solid #d0d0d0;
            z-index: 10;
        }

        .excel-style-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .excel-style-table tr:hover {
            background-color: #f0f7ff;
        }

        /* Table container styling */
        .table-container {
            overflow-x: auto;
            max-width: 100%;
            margin-bottom: 20px;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        /* Tooltip styling */
        .tooltip-cell {
            position: relative;
            cursor: help;
        }

        /* Custom tooltip styling for better visibility */
        [title] {
            position: relative;
            cursor: help;
        }

        [title]:hover::after {
            content: attr(title);
            position: absolute;
            bottom: 100%;
            left: 0;
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            white-space: normal;
            z-index: 100;
            max-width: 300px;
            word-wrap: break-word;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            pointer-events: none;
            margin-bottom: 5px;
        }

        /* Adjust tooltip position for table cells */
        table [title]:hover::after {
            white-space: pre-wrap;
            width: auto;
            min-width: 200px;
            max-width: 400px;
        }

        /* Column specific styling */
        #productsTable td:nth-child(1), #productsTable th:nth-child(1) { /* Item Code */
            min-width: 100px;
            font-weight: 500;
        }

        #productsTable td:nth-child(2), #productsTable th:nth-child(2) { /* Room */
            min-width: 100px;
        }

        #productsTable td:nth-child(3), #productsTable th:nth-child(3) { /* Item Name */
            min-width: 150px;
            font-weight: 500;
        }

        #productsTable td:nth-child(7), #productsTable th:nth-child(7) { /* Available Stock */
            text-align: right;
            min-width: 80px;
        }

        #productsTable td:nth-child(10), #productsTable th:nth-child(10), /* Unit Retail Price */
        #productsTable td:nth-child(11), #productsTable th:nth-child(11), /* Wholesale Price */
        #productsTable td:nth-child(12), #productsTable th:nth-child(12), /* Unit Cost */
        #productsTable td:nth-child(13), #productsTable th:nth-child(13) { /* Profit */
            text-align: right;
            min-width: 100px;
        }

        .actions-button {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .edit-button,
        .delete-button {
            margin: 0 5px;
            padding: 5px 10px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 14px;
        }

        .edit-button {
            background-color: #ffc107;
            color: white;
        }

        .edit-button:hover {
            background-color: #e0a800;
        }

        .delete-button {
            background-color: #dc3545;
            color: white;
        }

        .delete-button:hover {
            background-color: #c82333;
        }

        /* Status / Toast Message */
        .status-message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            border-radius: 5px;
            color: white;
            font-weight: bold;
            display: none;
            z-index: 1001;
        }

        .status-message.success {
            background-color: #28a745;
        }

        .status-message.error {
            background-color: #dc3545;
        }

        .status-message.warning {
            background-color: #ffc107;
            color: #212529;
        }

        /* Popup Overlay (Add and Edit) */
        .popup-overlay {
            display: none; /* Hidden by default */
            position: fixed;
            z-index: 1000;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5); /* Black w/ opacity */
            justify-content: center;
            align-items: center;
        }

        .popup-content {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
        }

        .close-popup {
            position: absolute;
            top: 10px;
            right: 10px;
            font-size: 24px;
            background: none;
            border: none;
            cursor: pointer;
        }

        /* Form Group */
        .form-group {
            margin-bottom: 15px;
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input[type="text"],
        .form-group input[type="number"],
        .form-group select,
        .form-group textarea {
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 60px;
        }

        .color-picker {
            display: flex;
            align-items: center;
        }

        .color-picker input[type="text"] {
            margin-left: 10px;
            width: 100px;
        }

        /* Button Styles */
        button[type="submit"]:not(.action-btn):not(.category-btn) {
            background-color: #28a745;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.3s, transform 0.2s;
            text-align: center;
            height: 40px;
        }

        button[type="submit"]:not(.action-btn):not(.category-btn):hover {
            background-color: #218838;
            transform: scale(1.05);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .popup-content {
                width: 95%;
            }

            .products-section {
                padding: 10px;
            }

            /* Action buttons mobile styling */
            .action-buttons-container {
                flex-wrap: wrap;
                justify-content: center;
                gap: 10px !important;
            }

            .action-btn,
            .category-btn,
            .add-product-block {
                padding: 6px 10px;
                font-size: 13px;
                margin-left: 0 !important;
                min-width: 0;
                flex: 1 1 auto;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 150px;
                height: 36px;
            }

            /* For very small screens, stack buttons vertically */
            @media (max-width: 480px) {
                .action-buttons-container {
                    flex-direction: column;
                    align-items: stretch;
                }

                .action-btn,
                .category-btn,
                .add-product-block {
                    max-width: 100%;
                    margin-bottom: 5px;
                }
            }

            #productsTable th,
            #productsTable td {
                padding: 6px;
                font-size: 13px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 150px;
            }

            .form-group {
                flex-direction: column;
            }

            .color-picker {
                flex-direction: column;
                align-items: flex-start;
            }

            .color-picker input[type="text"] {
                margin-left: 0;
                margin-top: 5px;
            }

            .search-bar input[type="text"] {
                max-width: 100%;
            }
        }

        /* Accessibility: Visually Hidden */
        .visually-hidden {
            position: absolute !important;
            height: 1px;
            width: 1px;
            overflow: hidden;
            clip: rect(1px, 1px, 1px, 1px);
            white-space: nowrap;
        }

        /* Additional CSS for table/cards toggle (mobile) */
        /* By default, show the table and hide cards */
        #productsTable {
            display: table;
        }
        .products-cards {
            display: none; /* Hidden by default on desktop */
        }
        /* When below 768px, hide the table and show the cards */
        @media (max-width: 768px) {
            #productsTable {
                display: none;
            }
            .products-cards {
                display: block;
            }
            /* Excel-like card layout styling */
            .product-card {
                border: 1px solid #e0e0e0;
                border-radius: 6px;
                padding: 16px;
                margin-bottom: 16px;
                background-color: #fff;
                box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            }
            .product-card .card-header {
                font-weight: bold;
                margin-bottom: 12px;
                padding-bottom: 8px;
                border-bottom: 1px solid #eee;
                color: #333;
            }
            .product-card .card-body {
                font-size: 14px;
                line-height: 1.5;
            }
            .product-card .card-body p {
                display: flex;
                justify-content: space-between;
                margin-bottom: 8px;
                border-bottom: 1px solid #f5f5f5;
                padding-bottom: 4px;
            }
            .product-card .card-body p strong {
                font-weight: 600;
                color: #555;
            }
            .product-card .card-actions {
                margin-top: 15px;
                display: flex;
                gap: 10px;
                padding-top: 10px;
                border-top: 1px solid #eee;
            }
            .product-card .edit-button,
            .product-card .delete-button {
                padding: 8px 14px;
                font-size: 14px;
                border-radius: 4px;
                border: none;
                cursor: pointer;
                transition: all 0.2s ease;
            }
            .product-card .edit-button {
                background-color: #ffc107;
                color: #fff;
            }
            .product-card .edit-button:hover {
                background-color: #e0a800;
            }
            .product-card .delete-button {
                background-color: #dc3545;
                color: #fff;
            }
            .product-card .delete-button:hover {
                background-color: #c82333;
            }
        }

        /* Confirmation Toast Styles */
        .confirmation-toast {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            padding: 24px;
            z-index: 10000;
            max-width: 400px;
            width: 90%;
            text-align: center;
            border: 1px solid #e0e0e0;
        }

        .confirmation-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 9999;
        }

        .confirmation-message {
            font-size: 1.1rem;
            color: #333;
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .confirmation-buttons {
            display: flex;
            gap: 12px;
            justify-content: center;
        }

        .confirm-btn, .cancel-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 80px;
        }

        .confirm-btn {
            background-color: #dc3545;
            color: white;
        }

        .confirm-btn:hover {
            background-color: #c82333;
        }

        .cancel-btn {
            background-color: #6c757d;
            color: white;
        }

        .cancel-btn:hover {
            background-color: #5a6268;
        }

        /* Prompt Toast Styles */
        .prompt-toast {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            padding: 24px;
            z-index: 10000;
            max-width: 450px;
            width: 90%;
            text-align: center;
            border: 1px solid #e0e0e0;
        }

        .prompt-message {
            font-size: 1.1rem;
            color: #333;
            margin-bottom: 16px;
            line-height: 1.5;
        }

        .prompt-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 1rem;
            margin-bottom: 20px;
            box-sizing: border-box;
        }

        .prompt-input:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
        }

        .prompt-buttons {
            display: flex;
            gap: 12px;
            justify-content: center;
        }

        .prompt-ok-btn, .prompt-cancel-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 80px;
        }

        .prompt-ok-btn {
            background-color: #4CAF50;
            color: white;
        }

        .prompt-ok-btn:hover {
            background-color: #45a049;
        }

        .prompt-cancel-btn {
            background-color: #6c757d;
            color: white;
        }

        .prompt-cancel-btn:hover {
            background-color: #5a6268;
        }

        /* Mobile adjustments for toasts */
        @media (max-width: 480px) {
            .confirmation-toast, .prompt-toast {
                padding: 20px;
                max-width: 320px;
            }

            .confirmation-message, .prompt-message {
                font-size: 1rem;
            }

            .confirm-btn, .cancel-btn, .prompt-ok-btn, .prompt-cancel-btn {
                padding: 10px 20px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div style="margin-bottom: 2%;">
            <a href="index.html" style="text-decoration: none; color: #007BFF; font-size: 16px; margin-right: 20px;">&larr; Back to Home</a>
        </div>
        <!-- Action Buttons -->
        <div class="action-buttons-container">
            <!-- Add Product Button -->
            <button id="addProductBlock" class="action-btn add-product-block" aria-label="Add new product" title="Add New Product">
                <span class="plus-icon">+</span> Add Product
            </button>

            <!-- Manage Categories Button -->
            <button id="manageCategoriesBtn" class="action-btn category-btn">
                <span class="button-icon">⚙️</span> Manage Categories
            </button>

            <!-- Manage Car Brands Button -->
            <button id="manageCarBrandsBtn" class="action-btn category-btn">
                <span class="button-icon">🚗</span> Manage Car Brands
            </button>

            <!-- Manage Car Models Button -->
            <button id="manageCarModelsBtn" class="action-btn category-btn">
                <span class="button-icon">🚘</span> Manage Car Models
            </button>
        </div>

        <!-- Filter Menu -->
        <div id="filterMenu" class="filter-menu" aria-label="Filter Products">
            <div class="filter-group">
                <label for="filterCarBrand">Car Brand</label>
                <select id="filterCarBrand">
                    <option value="">All Brands</option>
                    <!-- Dynamic options will be populated here -->
                </select>
            </div>
            <div class="filter-group">
                <label for="filterProductCategory">Product Category</label>
                <select id="filterProductCategory">
                    <option value="">All Categories</option>
                    <!-- Dynamic options will be populated here -->
                </select>
            </div>
            <!-- New Filter Groups -->
            <div class="filter-group">
                <label for="filterCarModel">Car Model</label>
                <select id="filterCarModel">
                    <option value="">All Models</option>
                    <!-- Dynamic options will be populated here -->
                </select>
            </div>
            <div class="filter-group">
                <label for="filterRoom">Room</label>
                <select id="filterRoom">
                    <option value="">All Rooms</option>
                    <!-- Dynamic options will be populated here -->
                </select>
            </div>
            <div class="filter-group">
                <label for="filterColourTape">Colour Tape</label>
                <select id="filterColourTape">
                    <option value="">All Colours</option>
                    <!-- Dynamic options will be populated here -->
                </select>
            </div>
        </div>

        <!-- Add Product Modal -->
        <div id="popupOverlay" class="popup-overlay" aria-hidden="true" role="dialog" aria-labelledby="addProductModalTitle">
            <div class="popup-content">
                <button id="closePopup" class="close-popup" aria-label="Close popup">&times;</button>
                <h2 id="addProductModalTitle">Add Product to Room</h2>
                <form id="addProductForm">
                    <div class="form-group">
                        <label for="itemCode">Item Code</label>
                        <input type="text" id="itemCode" name="itemCode" required>
                    </div>
                    <div class="form-group">
                        <label for="room">Room</label>
                        <select id="room" name="room" required>
                            <option value="">Select a room</option>
                            <!-- Options will be populated dynamically -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="itemName">Item Name</label>
                        <input type="text" id="itemName" name="itemName" required>
                    </div>
                    <div class="form-group">
                        <label for="carBrand">Car Brand</label>
                        <select id="carBrand" name="carBrand">
                            <option value="">Select a brand</option>
                            <!-- Options will be populated dynamically -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="carModel">Car Model</label>
                        <select id="carModel" name="carModel">
                            <option value="">Select a model</option>
                            <!-- Options will be populated dynamically -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="productCategory">Product Category</label>
                        <select id="productCategory" name="productCategory" required>
                            <option value="">Select a category</option>
                            <option value="lips">Lips</option>
                            <option value="boot_spoilers">Boot spoilers</option>
                            <option value="roof_spoilers">Roof spoilers</option>
                            <option value="diffusers">Diffusers</option>
                            <option value="sills_extensions">Sills extensions</option>
                            <option value="eyelids">Eyelids</option>
                            <option value="mirrors_covers">Mirrors covers</option>
                            <option value="grills">Grills</option>
                            <option value="canards">Canards</option>
                            <option value="bumper_spats">Bumper spats</option>
                            <option value="arches">Arches</option>
                            <option value="step_bars">Step bars</option>
                            <option value="bonnet_scoops">Bonnet scoops</option>
                            <option value="headlight_trims">Headlight trims</option>
                            <option value="tail_light_trims">Tail light trims</option>
                            <option value="main_grills">Main grills</option>
                            <option value="electronics">Electronics - radios, lights, cameras</option>
                            <option value="mats">Mats</option>
                            <option value="boot_mats">Boot mats</option>
                            <option value="nudge_bars">Nudge bars</option>
                            <option value="roll_bars">Roll bars</option>
                            <option value="fog_grills">Fog grills</option>
                            <option value="vinyl">Vinyl</option>
                            <!-- Add more categories as needed -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="availableStock">Available Stock</label>
                        <input type="number" id="availableStock" name="availableStock" required min="0">
                    </div>
                    <div class="form-group">
                        <label for="lowStockThreshold">Low Stock Warning Threshold</label>
                        <input type="number" id="lowStockThreshold" name="lowStockThreshold" value="5" min="1">
                        <small>Products with stock at or below this number will appear in the Low Stock page</small>
                    </div>

                    <div class="form-group">
                        <label for="minOrderQuantity">Min Order Quantity</label>
                        <input type="number" id="minOrderQuantity" name="minOrderQuantity" value="1" min="1">
                    </div>

                    <div class="form-group">
                        <label for="location">Location</label>
                        <input type="text" id="location" name="location">
                    </div>
                    <div class="form-group color-picker">
                        <label for="colourTape">Colour Tape</label>
                        <input type="color" id="colourTape" name="colourTape" value="#000000">
                        <input type="text" id="colourTapeText" name="colourTapeText" value="#000000" pattern="^#[0-9A-Fa-f]{6}$" title="Enter a valid hex color code">
                    </div>
                    <div class="form-group">
                        <label for="unitRetailPrice">Unit Retail Price (ZAR)</label>
                        <input type="number" step="0.01" id="unitRetailPrice" name="unitRetailPrice" required min="0">
                    </div>
                    <div class="form-group">
                        <label for="wholesalePrice">Wholesale Price (ZAR)</label>
                        <input type="number" step="0.01" id="wholesalePrice" name="wholesalePrice" required min="0">
                    </div>
                    <div class="form-group">
                        <label for="unitCost">Unit Cost (ZAR)</label>
                        <input type="number" step="0.01" id="unitCost" name="unitCost" required min="0">
                    </div>
                    <div class="form-group">
                        <label for="supplierCode">Supplier Code</label>
                        <input type="text" id="supplierCode" name="supplierCode">
                    </div>
                    <div class="form-group">
                        <label for="additionalComments">Additional Comments</label>
                        <textarea id="additionalComments" name="additionalComments"></textarea>
                    </div>
                    <button type="submit" class="action-btn">Add Product</button>
                </form>
            </div>
        </div>

        <!-- Edit Product Modal -->
        <div id="editProductOverlay" class="popup-overlay" aria-hidden="true" role="dialog" aria-labelledby="editProductModalTitle">
            <div class="popup-content">
                <button id="closeEditPopup" class="close-popup" aria-label="Close popup">&times;</button>
                <h2 id="editProductModalTitle">Edit Product</h2>
                <form id="editProductForm">
                    <input type="hidden" id="editItemCode" name="editItemCode">

                    <div class="form-group">
                        <label for="editRoom">
                            Room
                            <span style="color: #666; font-size: 0.9em;">(Change to move product to different room)</span>
                            <span style="color: #28a745; font-size: 0.8em; margin-left: 5px;">✓ Move between rooms enabled</span>
                        </label>
                        <select id="editRoom" name="editRoom" required style="background-color: #f8f9fa; border: 2px solid #007bff;">
                            <option value="">Select a room</option>
                            <!-- Options will be populated dynamically -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="editItemName">Item Name</label>
                        <input type="text" id="editItemName" name="editItemName" required>
                    </div>
                    <div class="form-group">
                        <label for="editCarBrand">Car Brand</label>
                        <select id="editCarBrand" name="editCarBrand">
                            <option value="">Select a brand</option>
                            <!-- Options will be populated dynamically -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="editCarModel">Car Model</label>
                        <select id="editCarModel" name="editCarModel">
                            <option value="">Select a model</option>
                            <!-- Options will be populated dynamically -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="editProductCategory">Product Category</label>
                        <select id="editProductCategory" name="editProductCategory" required>
                            <option value="">Select a category</option>
                            <option value="lips">Lips</option>
                            <option value="boot_spoilers">Boot spoilers</option>
                            <option value="roof_spoilers">Roof spoilers</option>
                            <option value="diffusers">Diffusers</option>
                            <option value="sills_extensions">Sills extensions</option>
                            <option value="eyelids">Eyelids</option>
                            <option value="mirrors_covers">Mirrors covers</option>
                            <option value="grills">Grills</option>
                            <option value="canards">Canards</option>
                            <option value="bumper_spats">Bumper spats</option>
                            <option value="arches">Arches</option>
                            <option value="step_bars">Step bars</option>
                            <option value="bonnet_scoops">Bonnet scoops</option>
                            <option value="headlight_trims">Headlight trims</option>
                            <option value="tail_light_trims">Tail light trims</option>
                            <option value="main_grills">Main grills</option>
                            <option value="electronics">Electronics - radios, lights, cameras</option>
                            <option value="mats">Mats</option>
                            <option value="boot_mats">Boot mats</option>
                            <option value="nudge_bars">Nudge bars</option>
                            <option value="roll_bars">Roll bars</option>
                            <option value="fog_grills">Fog grills</option>
                            <option value="vinyl">Vinyl</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="editAvailableStock">Available Stock</label>
                        <input type="number" id="editAvailableStock" name="editAvailableStock" required min="0">
                    </div>
                    <div class="form-group">
                        <label for="editLowStockThreshold">Low Stock Warning Threshold</label>
                        <input type="number" id="editLowStockThreshold" name="editLowStockThreshold" value="5" min="1">
                        <small>Products with stock at or below this number will appear in the Low Stock page</small>
                    </div>

                    <div class="form-group" style="display: none;">
                        <label for="editMinOrderQuantity">Min Order Quantity</label>
                        <input type="number" id="editMinOrderQuantity" name="editMinOrderQuantity" value="1" min="1">
                    </div>

                    <div class="form-group">
                        <label for="editLocation">Location</label>
                        <input type="text" id="editLocation" name="editLocation">
                    </div>
                    <div class="form-group color-picker">
                        <label for="editColourTape">Colour Tape</label>
                        <input type="color" id="editColourTape" name="editColourTape" value="#000000">
                        <input type="text" id="editColourTapeText" name="editColourTapeText" value="#000000" pattern="^#[0-9A-Fa-f]{6}$" title="Enter a valid hex color code">
                    </div>
                    <div class="form-group">
                        <label for="editUnitRetailPrice">Unit Retail Price (ZAR)</label>
                        <input type="number" step="0.01" id="editUnitRetailPrice" name="editUnitRetailPrice" required min="0">
                    </div>
                    <div class="form-group">
                        <label for="editWholesalePrice">Wholesale Price (ZAR)</label>
                        <input type="number" step="0.01" id="editWholesalePrice" name="editWholesalePrice" required min="0">
                    </div>
                    <div class="form-group">
                        <label for="editUnitCost">Unit Cost (ZAR)</label>
                        <input type="number" step="0.01" id="editUnitCost" name="editUnitCost" required min="0">
                    </div>
                    <div class="form-group">
                        <label for="editSupplierCode">Supplier Code</label>
                        <input type="text" id="editSupplierCode" name="editSupplierCode">
                    </div>
                    <div class="form-group">
                        <label for="editAdditionalComments">Additional Comments</label>
                        <textarea id="editAdditionalComments" name="editAdditionalComments"></textarea>
                    </div>
                    <button type="submit" class="action-btn">Update Product</button>
                </form>
            </div>
        </div>

        <!-- Products Listing Section -->
        <div class="products-section">
            <h2>Products List</h2>

            <!-- Search Bar -->
            <div class="search-bar">
                <label for="searchInput" class="visually-hidden">Search Products</label>
                <input type="text" id="searchInput" placeholder="Search products by name..." aria-label="Search Products">
            </div>

            <table id="productsTable" class="excel-style-table">
                <thead>
                    <tr>
                        <th>Item Code</th>
                        <th>Room</th>
                        <th>Item Name</th>
                        <th>Car Brand</th>
                        <th>Car Model</th>
                        <th>Category</th>
                        <th>Available Stock</th>
                        <th>Low Stock Threshold</th>
                        <th>Colour Tape</th>
                        <th>Location</th>
                        <th>Unit Retail Price (ZAR)</th>
                        <th>Wholesale Price (ZAR)</th>
                        <th>Unit Cost (ZAR)</th>
                        <th>Profit (ZAR)</th>
                        <th>Supplier Code</th>
                        <th>Additional Comments</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Products will be populated dynamically -->
                </tbody>
            </table>
        </div>

        <!-- Status Message (Toast-like) -->
        <div id="statusMessage" class="status-message"></div>

        <!-- Cards Container (Mobile) -->
        <div id="productsCards" class="products-cards">
            <!-- Cards will be dynamically inserted here -->
        </div>

        <!-- Categories Modal -->
        <div id="categoriesOverlay" class="popup-overlay" aria-hidden="true" role="dialog" aria-labelledby="categoriesModalTitle">
            <div class="popup-content">
                <button id="closeCategoriesPopup" class="close-popup" aria-label="Close popup">&times;</button>
                <h2 id="categoriesModalTitle">Manage Product Categories</h2>

                <!-- Add Category Form -->
                <form id="addCategoryForm" style="margin-bottom: 20px;">
                    <div style="display: flex; gap: 10px;">
                        <div class="form-group" style="flex-grow: 1;">
                            <label for="categoryName">New Category Name</label>
                            <input type="text" id="categoryName" name="categoryName" required placeholder="Enter category name">
                        </div>
                        <div style="display: flex; align-items: flex-end;">
                            <button type="submit" class="action-btn category-btn">
                                <span class="button-icon">+</span> Add Category
                            </button>
                        </div>
                    </div>
                </form>

                <!-- Categories List -->
                <div style="margin-top: 20px;">
                    <h3>Existing Categories</h3>
                    <div class="table-container">
                        <table id="categoriesTable" class="excel-style-table">
                            <thead>
                                <tr>
                                    <th>Category Name</th>
                                    <th>Value</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="categoriesTableBody">
                                <!-- Categories will be populated dynamically -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Car Brands Modal -->
        <div id="carBrandsOverlay" class="popup-overlay" aria-hidden="true" role="dialog" aria-labelledby="carBrandsModalTitle">
            <div class="popup-content">
                <button id="closeCarBrandsPopup" class="close-popup" aria-label="Close popup">&times;</button>
                <h2 id="carBrandsModalTitle">Manage Car Brands</h2>

                <!-- Add Car Brand Form -->
                <form id="addCarBrandForm" style="margin-bottom: 20px;">
                    <div style="display: flex; gap: 10px;">
                        <div class="form-group" style="flex-grow: 1;">
                            <label for="brandName">New Brand Name</label>
                            <input type="text" id="brandName" name="brandName" required placeholder="Enter brand name">
                        </div>
                        <div style="display: flex; align-items: flex-end;">
                            <button type="submit" class="action-btn category-btn">
                                <span class="button-icon">+</span> Add Brand
                            </button>
                        </div>
                    </div>
                </form>

                <!-- Car Brands List -->
                <div style="margin-top: 20px;">
                    <h3>Existing Car Brands</h3>
                    <div class="table-container">
                        <table id="carBrandsTable" class="excel-style-table">
                            <thead>
                                <tr>
                                    <th>Brand Name</th>
                                    <th>Value</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="carBrandsTableBody">
                                <!-- Car brands will be populated dynamically -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Car Models Modal -->
        <div id="carModelsOverlay" class="popup-overlay" aria-hidden="true" role="dialog" aria-labelledby="carModelsModalTitle">
            <div class="popup-content">
                <button id="closeCarModelsPopup" class="close-popup" aria-label="Close popup">&times;</button>
                <h2 id="carModelsModalTitle">Manage Car Models</h2>

                <!-- Add Car Model Form -->
                <form id="addCarModelForm" style="margin-bottom: 20px;">
                    <div style="display: flex; flex-direction: column; gap: 10px;">
                        <div class="form-group">
                            <label for="modelBrandId">Car Brand</label>
                            <select id="modelBrandId" name="modelBrandId" required>
                                <option value="">Select a brand</option>
                                <!-- Options will be populated dynamically -->
                            </select>
                        </div>
                        <div style="display: flex; gap: 10px;">
                            <div class="form-group" style="flex-grow: 1;">
                                <label for="modelName">New Model Name</label>
                                <input type="text" id="modelName" name="modelName" required placeholder="Enter model name">
                            </div>
                            <div style="display: flex; align-items: flex-end;">
                                <button type="submit" class="action-btn category-btn">
                                    <span class="button-icon">+</span> Add Model
                                </button>
                            </div>
                        </div>
                    </div>
                </form>

                <!-- Car Models List -->
                <div style="margin-top: 20px;">
                    <h3>Existing Car Models</h3>
                    <div class="form-group" style="margin-bottom: 20px;">
                        <label for="filterModelBrand">Filter by Brand</label>
                        <select id="filterModelBrand" name="filterModelBrand">
                            <option value="">All Brands</option>
                            <!-- Options will be populated dynamically -->
                        </select>
                    </div>
                    <div class="table-container">
                        <table id="carModelsTable" class="excel-style-table">
                            <thead>
                                <tr>
                                    <th>Model Name</th>
                                    <th>Brand</th>
                                    <th>Value</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="carModelsTableBody">
                                <!-- Car models will be populated dynamically -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <script>
            /**
             * Capitalize words helper (used in both table & cards)
             */
            function capitalizeWords(str) {
                return str.replace(/\b\w/g, char => char.toUpperCase());
            }

            /**
             * Render the products as cards in the mobile view
             * @param {Array} products - Array of product objects to display
             */
            function renderProductsCards(products) {
                const productsCardsContainer = document.getElementById('productsCards');
                // Clear existing cards
                productsCardsContainer.innerHTML = '';

                // Create cards for each product
                products.forEach(product => {
                    // Create card wrapper
                    const card = document.createElement('div');
                    card.classList.add('product-card');

                    // Card header (e.g., item name)
                    const cardHeader = document.createElement('div');
                    cardHeader.classList.add('card-header');
                    cardHeader.textContent = product.item_name || 'No Name';
                    cardHeader.title = product.item_name || 'No Name'; // Add tooltip
                    card.appendChild(cardHeader);

                    // Card body (details)
                    const cardBody = document.createElement('div');
                    cardBody.classList.add('card-body');

                    // Build the textual info
                    cardBody.innerHTML = `
                        <p><strong>Item Code:</strong> <span>${product.item_code}</span></p>
                        <p><strong>Room:</strong> <span>${product.room_name}</span></p>
                        <p><strong>Car Brand:</strong> <span>${product.car_brand || 'N/A'}</span></p>
                        <p><strong>Car Model:</strong> <span>${product.car_model || 'N/A'}</span></p>
                        <p><strong>Category:</strong> <span>${capitalizeWords(product.product_category.replace(/_/g, ' '))}</span></p>
                        <p><strong>Available Stock:</strong> <span>${product.available_stock}</span></p>
                        <p><strong>Low Stock Threshold:</strong> <span>${product.low_stock_threshold || 5}</span></p>
                        <p><strong>Colour Tape:</strong>
                            <span style="display:inline-block;width:20px;height:20px;background-color:${product.colour_tape};border-radius:50%;border:1px solid #ddd;"></span>
                        </p>
                        <p><strong>Location:</strong> <span>${product.location || 'N/A'}</span></p>
                        <p><strong>Unit Retail Price:</strong> <span>R${Math.round(parseFloat(product.unit_retail_price)).toLocaleString()}</span></p>
                        <p><strong>Wholesale Price:</strong> <span>R${Math.round(parseFloat(product.wholesale_price)).toLocaleString()}</span></p>
                        <p><strong>Unit Cost:</strong> <span>R${Math.round(parseFloat(product.unit_cost)).toLocaleString()}</span></p>
                        <p><strong>Profit:</strong> <span>R${Math.round(parseFloat(product.profit)).toLocaleString()}</span></p>
                        <p><strong>Supplier Code:</strong> <span>${product.supplier_code || 'N/A'}</span></p>
                        <p><strong>Comments:</strong> <span>${product.additional_comments || 'None'}</span></p>
                    `;
                    card.appendChild(cardBody);

                    // Card actions (Edit/Delete buttons)
                    const cardActions = document.createElement('div');
                    cardActions.classList.add('card-actions');

                    // Edit button
                    const editButton = document.createElement('button');
                    editButton.classList.add('edit-button');
                    editButton.setAttribute('data-item-code', product.item_code);
                    editButton.setAttribute('data-room-id', product.room_id);
                    editButton.textContent = 'Edit';
                    cardActions.appendChild(editButton);

                    // Delete button
                    const deleteButton = document.createElement('button');
                    deleteButton.classList.add('delete-button');
                    deleteButton.setAttribute('data-item-code', product.item_code);
                    deleteButton.setAttribute('data-room-id', product.room_id);
                    deleteButton.textContent = 'Delete';
                    cardActions.appendChild(deleteButton);

                    card.appendChild(cardActions);

                    // Append to container
                    productsCardsContainer.appendChild(card);
                });
            }
        </script>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Auto-detect environment and set appropriate API URL
                const API_URL = (() => {
                  const hostname = window.location.hostname;

                  // Production environment (Netlify)
                  if (hostname === 'shans-system.netlify.app') {
                    return 'https://shans-backend.onrender.com/api';
                  }

                  // Local development
                  if (hostname === 'localhost' || hostname === '127.0.0.1') {
                    return 'https://shans-backend.onrender.com/api';
                  }

                  // Default fallback to production
                  return 'https://shans-backend.onrender.com/api';
                })();

                // **Element References**
                // Add Product Modal Elements
                const addProductBlock = document.getElementById('addProductBlock');
                const popupOverlay = document.getElementById('popupOverlay');
                const closePopup = document.getElementById('closePopup');
                const addProductForm = document.getElementById('addProductForm');
                const colourTape = document.getElementById('colourTape');
                const colourTapeText = document.getElementById('colourTapeText');

                // Categories Modal Elements
                const manageCategoriesBtn = document.getElementById('manageCategoriesBtn');
                const categoriesOverlay = document.getElementById('categoriesOverlay');
                const closeCategoriesPopup = document.getElementById('closeCategoriesPopup');
                const addCategoryForm = document.getElementById('addCategoryForm');
                const categoriesTableBody = document.getElementById('categoriesTableBody');

                // Car Brands Modal Elements
                const manageCarBrandsBtn = document.getElementById('manageCarBrandsBtn');
                const carBrandsOverlay = document.getElementById('carBrandsOverlay');
                const closeCarBrandsPopup = document.getElementById('closeCarBrandsPopup');
                const addCarBrandForm = document.getElementById('addCarBrandForm');
                const carBrandsTableBody = document.getElementById('carBrandsTableBody');

                // Car Models Modal Elements
                const manageCarModelsBtn = document.getElementById('manageCarModelsBtn');
                const carModelsOverlay = document.getElementById('carModelsOverlay');
                const closeCarModelsPopup = document.getElementById('closeCarModelsPopup');
                const addCarModelForm = document.getElementById('addCarModelForm');
                const carModelsTableBody = document.getElementById('carModelsTableBody');
                const modelBrandId = document.getElementById('modelBrandId');
                const filterModelBrand = document.getElementById('filterModelBrand');

                // Edit Product Modal Elements
                const editProductOverlay = document.getElementById('editProductOverlay');
                const closeEditPopup = document.getElementById('closeEditPopup');
                const editProductForm = document.getElementById('editProductForm');
                const editColourTape = document.getElementById('editColourTape');
                const editColourTapeText = document.getElementById('editColourTapeText');

                // Products Table and Status Message
                const productsTableBody = document.querySelector('#productsTable tbody');
                const statusMessage = document.getElementById('statusMessage');
                const searchInput = document.getElementById('searchInput');

                // Cards container
                const productsCardsContainer = document.getElementById('productsCards');

                // Filter Menu Elements
                const filterCarBrand = document.getElementById('filterCarBrand');
                const filterProductCategory = document.getElementById('filterProductCategory');
                const filterCarModel = document.getElementById('filterCarModel');
                const filterRoom = document.getElementById('filterRoom');
                const filterColourTape = document.getElementById('filterColourTape');

                // **Data Storage**
                let allProducts = []; // To store all fetched products

                // Store categories data
                let categories = [];

                // Store car brands data
                let carBrands = [];

                // Store car models data
                let carModels = [];

                // Local cache for products when database connection fails
                let localProductCache = {
                    pendingUpdates: {},

                    // Save a product update to local cache
                    saveUpdate: function(itemCode, productData) {
                        this.pendingUpdates[itemCode] = productData;
                        localStorage.setItem('pendingProductUpdates', JSON.stringify(this.pendingUpdates));
                        console.log('Saved product update to local cache:', itemCode, productData);
                    },

                    // Load pending updates from localStorage
                    loadPendingUpdates: function() {
                        const saved = localStorage.getItem('pendingProductUpdates');
                        if (saved) {
                            try {
                                this.pendingUpdates = JSON.parse(saved);
                                console.log('Loaded pending updates from cache:', Object.keys(this.pendingUpdates).length);
                            } catch (e) {
                                console.error('Error loading pending updates:', e);
                                this.pendingUpdates = {};
                            }
                        }
                    },

                    // Apply pending updates to the products array
                    applyPendingUpdates: function(products) {
                        for (const itemCode in this.pendingUpdates) {
                            const index = products.findIndex(p => p.item_code === itemCode);
                            if (index !== -1) {
                                // Merge the cached update with the product
                                products[index] = { ...products[index], ...this.pendingUpdates[itemCode] };
                                console.log('Applied cached update to product:', itemCode);
                            }
                        }
                        return products;
                    },

                    // Remove a pending update after it's successfully saved to the database
                    removePendingUpdate: function(itemCode) {
                        if (this.pendingUpdates[itemCode]) {
                            delete this.pendingUpdates[itemCode];
                            localStorage.setItem('pendingProductUpdates', JSON.stringify(this.pendingUpdates));
                            console.log('Removed pending update for:', itemCode);
                        }
                    }
                };

                // **Helper Functions**

                /**
                 * Function to determine appropriate text color based on background color for readability
                 * @param {string} hexcolor - The background color in hex format (e.g., "#FFFFFF")
                 * @returns {string} - Returns 'black' or 'white' based on the background color
                 */
                function getContrastYIQ(hexcolor) {
                    hexcolor = hexcolor.replace("#", "");
                    if (hexcolor.length === 3) {
                        hexcolor = hexcolor.split('').map(char => char + char).join('');
                    }
                    const r = parseInt(hexcolor.substr(0,2),16);
                    const g = parseInt(hexcolor.substr(2,2),16);
                    const b = parseInt(hexcolor.substr(4,2),16);
                    const yiq = ((r*299)+(g*587)+(b*114))/1000;
                    return (yiq >= 128) ? 'black' : 'white';
                }

                /**
                 * Show a temporary status (toast-like) message in the top-right corner
                 * @param {string} message - The message to display
                 * @param {string} type - 'success' or 'error'
                 */
                function showStatusMessage(message, type) {
                    statusMessage.textContent = message;
                    statusMessage.className = `status-message ${type}`;
                    statusMessage.style.display = 'block';

                    setTimeout(() => {
                        statusMessage.style.display = 'none';
                    }, 3000);
                }

                /**
                 * Confirmation toast system for Android WebView compatibility
                 * @param {string} message - The confirmation message
                 * @param {function} onConfirm - Callback when user confirms
                 * @param {function} onCancel - Callback when user cancels
                 */
                function showConfirmationToast(message, onConfirm, onCancel = null) {
                    // Create overlay
                    const overlay = document.createElement('div');
                    overlay.className = 'confirmation-overlay';

                    // Create toast
                    const toast = document.createElement('div');
                    toast.className = 'confirmation-toast';

                    // Create message
                    const messageDiv = document.createElement('div');
                    messageDiv.className = 'confirmation-message';
                    messageDiv.textContent = message;

                    // Create buttons container
                    const buttonsDiv = document.createElement('div');
                    buttonsDiv.className = 'confirmation-buttons';

                    // Create confirm button
                    const confirmBtn = document.createElement('button');
                    confirmBtn.className = 'confirm-btn';
                    confirmBtn.textContent = 'Yes';
                    confirmBtn.onclick = () => {
                        document.body.removeChild(overlay);
                        if (onConfirm) onConfirm();
                    };

                    // Create cancel button
                    const cancelBtn = document.createElement('button');
                    cancelBtn.className = 'cancel-btn';
                    cancelBtn.textContent = 'Cancel';
                    cancelBtn.onclick = () => {
                        document.body.removeChild(overlay);
                        if (onCancel) onCancel();
                    };

                    // Assemble the toast
                    buttonsDiv.appendChild(confirmBtn);
                    buttonsDiv.appendChild(cancelBtn);
                    toast.appendChild(messageDiv);
                    toast.appendChild(buttonsDiv);
                    overlay.appendChild(toast);

                    // Add to page
                    document.body.appendChild(overlay);

                    // Close on overlay click
                    overlay.onclick = (e) => {
                        if (e.target === overlay) {
                            document.body.removeChild(overlay);
                            if (onCancel) onCancel();
                        }
                    };
                }

                /**
                 * Prompt toast system for Android WebView compatibility
                 * @param {string} message - The prompt message
                 * @param {string} defaultValue - Default input value
                 * @param {function} onOk - Callback when user clicks OK (receives input value)
                 * @param {function} onCancel - Callback when user cancels
                 */
                function showPromptToast(message, defaultValue = '', onOk, onCancel = null) {
                    // Create overlay
                    const overlay = document.createElement('div');
                    overlay.className = 'confirmation-overlay';

                    // Create toast
                    const toast = document.createElement('div');
                    toast.className = 'prompt-toast';

                    // Create message
                    const messageDiv = document.createElement('div');
                    messageDiv.className = 'prompt-message';
                    messageDiv.textContent = message;

                    // Create input
                    const input = document.createElement('input');
                    input.type = 'text';
                    input.className = 'prompt-input';
                    input.value = defaultValue;
                    input.placeholder = 'Enter value...';

                    // Create buttons container
                    const buttonsDiv = document.createElement('div');
                    buttonsDiv.className = 'prompt-buttons';

                    // Create OK button
                    const okBtn = document.createElement('button');
                    okBtn.className = 'prompt-ok-btn';
                    okBtn.textContent = 'OK';
                    okBtn.onclick = () => {
                        const value = input.value.trim();
                        document.body.removeChild(overlay);
                        if (onOk) onOk(value);
                    };

                    // Create cancel button
                    const cancelBtn = document.createElement('button');
                    cancelBtn.className = 'prompt-cancel-btn';
                    cancelBtn.textContent = 'Cancel';
                    cancelBtn.onclick = () => {
                        document.body.removeChild(overlay);
                        if (onCancel) onCancel();
                    };

                    // Handle Enter key
                    input.onkeydown = (e) => {
                        if (e.key === 'Enter') {
                            okBtn.click();
                        } else if (e.key === 'Escape') {
                            cancelBtn.click();
                        }
                    };

                    // Assemble the toast
                    buttonsDiv.appendChild(okBtn);
                    buttonsDiv.appendChild(cancelBtn);
                    toast.appendChild(messageDiv);
                    toast.appendChild(input);
                    toast.appendChild(buttonsDiv);
                    overlay.appendChild(toast);

                    // Add to page
                    document.body.appendChild(overlay);

                    // Focus input and select text
                    setTimeout(() => {
                        input.focus();
                        input.select();
                    }, 100);

                    // Close on overlay click
                    overlay.onclick = (e) => {
                        if (e.target === overlay) {
                            document.body.removeChild(overlay);
                            if (onCancel) onCancel();
                        }
                    };
                }

                /**
                 * Function to open the Add Product modal
                 */
                function openAddModal() {
                    popupOverlay.style.display = 'flex';
                    popupOverlay.setAttribute('aria-hidden', 'false');

                    // Ensure dropdowns are populated when modal opens
                    setTimeout(() => {
                        const carBrandSelect = document.getElementById('carBrand');
                        const carModelSelect = document.getElementById('carModel');

                        if (carBrandSelect && carBrands.length > 0) {
                            updateSingleCarBrandDropdown(carBrandSelect, 'carBrand');
                        }

                        // Clear car model dropdown initially (user needs to select brand first)
                        if (carModelSelect) {
                            while (carModelSelect.options.length > 1) {
                                carModelSelect.remove(1);
                            }
                        }
                    }, 100);
                }

                /**
                 * Function to close the Add Product modal
                 */
                function closeAddModal() {
                    popupOverlay.style.display = 'none';
                    popupOverlay.setAttribute('aria-hidden', 'true');
                    addProductForm.reset();
                    colourTape.value = '#000000';
                    colourTapeText.value = '#000000';
                }

                /**
                 * Function to open the Edit Product modal
                 * @param {Object} product - The product data to edit
                 */
                function openEditModal(product) {
                    console.log('Opening edit modal with product data:', product);

                    // Store the original room ID for comparison
                    originalRoomId = product.room_id ? product.room_id.toString() : null;

                    editProductOverlay.style.display = 'flex';
                    editProductOverlay.setAttribute('aria-hidden', 'false');

                    // Ensure dropdowns are populated when modal opens
                    setTimeout(() => {
                        const editCarBrandSelect = document.getElementById('editCarBrand');
                        const editCarModelSelect = document.getElementById('editCarModel');
                        const editRoomSelect = document.getElementById('editRoom');

                        if (editCarBrandSelect && carBrands.length > 0) {
                            updateSingleCarBrandDropdown(editCarBrandSelect, 'editCarBrand');
                        }

                        // Ensure room dropdown is populated and set the current room
                        if (editRoomSelect && product.room_id) {
                            // Set the current room value
                            editRoomSelect.value = product.room_id;
                            // Reset styling to default
                            editRoomSelect.style.backgroundColor = '#f8f9fa';
                            editRoomSelect.style.borderColor = '#007bff';
                            console.log('Set room dropdown to:', product.room_id, 'Available options:', Array.from(editRoomSelect.options).map(opt => ({value: opt.value, text: opt.text})));
                        }

                        // If product has a car brand, populate the models for that brand
                        if (product.car_brand && editCarModelSelect) {
                            const brand = carBrands.find(b => b.name === product.car_brand);
                            if (brand) {
                                fetchCarModelsByBrand(brand.id).then(brandModels => {
                                    // Clear existing options except the first one
                                    while (editCarModelSelect.options.length > 1) {
                                        editCarModelSelect.remove(1);
                                    }

                                    // Add options for each model
                                    brandModels.forEach(model => {
                                        const option = document.createElement('option');
                                        option.value = model.name;
                                        option.textContent = model.name;
                                        editCarModelSelect.appendChild(option);
                                    });

                                    // Set the selected model
                                    if (product.car_model) {
                                        editCarModelSelect.value = product.car_model;
                                    }
                                });
                            }
                        }
                    }, 100);

                    // Prefill the form with product data
                    document.getElementById('editItemCode').value = product.item_code;
                    document.getElementById('editRoom').value = product.room_id;
                    document.getElementById('editItemName').value = product.item_name;
                    document.getElementById('editCarBrand').value = product.car_brand || '';
                    document.getElementById('editCarModel').value = product.car_model || '';
                    document.getElementById('editProductCategory').value = product.product_category;
                    document.getElementById('editAvailableStock').value = product.available_stock;

                    // Set the low stock threshold
                    const lowStockThreshold = parseInt(product.low_stock_threshold);
                    document.getElementById('editLowStockThreshold').value = isNaN(lowStockThreshold) ? 5 : lowStockThreshold;

                    // Make sure min_order_quantity is properly set
                    console.log('Setting min_order_quantity:', product.min_order_quantity);
                    // Force the min_order_quantity to be a number and default to 1 if not valid
                    const minOrderQty = parseInt(product.min_order_quantity);
                    document.getElementById('editMinOrderQuantity').value = isNaN(minOrderQty) ? 1 : minOrderQty;

                    // Set location
                    document.getElementById('editLocation').value = product.location || '';

                    // Set prices - ensure we're handling all possible formats
                    const unitRetailPrice = parseFloat(product.unit_retail_price);
                    document.getElementById('editUnitRetailPrice').value = isNaN(unitRetailPrice) ? 0 : unitRetailPrice;

                    const wholesalePrice = parseFloat(product.wholesale_price);
                    document.getElementById('editWholesalePrice').value = isNaN(wholesalePrice) ? 0 : wholesalePrice;

                    const unitCost = parseFloat(product.unit_cost);
                    document.getElementById('editUnitCost').value = isNaN(unitCost) ? 0 : unitCost;

                    // Set supplier code and comments
                    document.getElementById('editSupplierCode').value = product.supplier_code || '';
                    document.getElementById('editAdditionalComments').value = product.additional_comments || '';

                    // Set color tape
                    editColourTape.value = product.colour_tape || '#000000';
                    editColourTapeText.value = product.colour_tape || '#000000';

                    // Log the values we're setting for debugging
                    console.log('Set unit retail price:', document.getElementById('editUnitRetailPrice').value);
                    console.log('Set wholesale price:', document.getElementById('editWholesalePrice').value);
                    console.log('Set unit cost:', document.getElementById('editUnitCost').value);
                }

                /**
                 * Function to close the Edit Product modal
                 */
                function closeEditModal() {
                    editProductOverlay.style.display = 'none';
                    editProductOverlay.setAttribute('aria-hidden', 'true');
                    editProductForm.reset();
                    editColourTape.value = '#000000';
                    editColourTapeText.value = '#000000';

                    // Clean up room move message and reset original room ID
                    const moveMessage = document.getElementById('roomMoveMessage');
                    if (moveMessage) {
                        moveMessage.remove();
                    }
                    originalRoomId = null;
                }

                /**
                 * Check URL parameters for edit_item and open the edit popup if found
                 */
                async function checkForEditItemParam() {
                    const urlParams = new URLSearchParams(window.location.search);
                    const editItemCode = urlParams.get('edit_item');

                    if (editItemCode) {
                        // Wait a short delay to ensure the products have loaded
                        setTimeout(async () => {
                            try {
                                // First check if the product is already in our loaded products
                                const existingProduct = allProducts.find(p => p.item_code === editItemCode);

                                if (existingProduct) {
                                    // If we already have the product data, use it directly
                                    console.log('Product found in loaded data:', existingProduct);
                                    openEditModal(existingProduct);
                                } else {
                                    // If not found in loaded products, fetch it from the API
                                    showStatusMessage('Loading product details...', 'warning');

                                    const response = await fetch(`${API_URL}/products/${encodeURIComponent(editItemCode)}`);
                                    if (!response.ok) {
                                        throw new Error('Failed to fetch product details');
                                    }

                                    const product = await response.json();
                                    console.log('Product data fetched from API:', product);

                                    // Open the edit modal with the product data
                                    openEditModal(product);

                                    // Clear the status message
                                    document.getElementById('statusMessage').style.display = 'none';
                                }

                                // Remove the parameter from the URL to prevent reopening on refresh
                                const newUrl = window.location.pathname;
                                window.history.replaceState({}, document.title, newUrl);
                            } catch (error) {
                                console.error('Error fetching product details from URL parameter:', error);
                                showStatusMessage('Failed to load product details. Please try again.', 'error');
                            }
                        }, 500); // 500ms delay to ensure products are loaded
                    }
                }

                /**
                 * Fetch all rooms from the backend and populate the room dropdowns
                 */
                async function fetchRooms() {
                    try {
                        const response = await fetch(`${API_URL}/rooms`);
                        if (!response.ok) {
                            throw new Error('Failed to fetch rooms');
                        }
                        const rooms = await response.json();

                        const roomSelect = document.getElementById('room');
                        const editRoomSelect = document.getElementById('editRoom');

                        // Clear existing options except the first
                        roomSelect.innerHTML = '<option value="">Select a room</option>';
                        editRoomSelect.innerHTML = '<option value="">Select a room</option>';

                        rooms.forEach(room => {
                            const option = document.createElement('option');
                            option.value = room.id;
                            option.textContent = room.name;
                            roomSelect.appendChild(option.cloneNode(true));
                            editRoomSelect.appendChild(option.cloneNode(true));
                        });
                    } catch (error) {
                        console.error('Error fetching rooms:', error);
                        showStatusMessage('Failed to load rooms. Please try again later.', 'error');
                    }
                }

                /**
                 * Fetch all products from the backend and populate the products table
                 * @param {boolean} suppressErrorMessage - If true, don't show error messages
                 * @returns {Promise<boolean>} - True if successful, false if failed
                 */
                async function fetchProducts(suppressErrorMessage = false) {
                    try {
                        // Load any pending updates from localStorage
                        localProductCache.loadPendingUpdates();

                        const response = await fetch(`${API_URL}/products`);
                        if (!response.ok) {
                            throw new Error('Failed to fetch products');
                        }
                        let products = await response.json();

                        // Apply any pending updates from the local cache
                        products = localProductCache.applyPendingUpdates(products);

                        allProducts = products; // Store all products

                        renderProductsTable(allProducts);
                        populateFilters(); // Populate filter options after fetching products

                        return true; // Success
                    } catch (error) {
                        console.error('Error fetching products:', error);

                        // Only show error message if not suppressed
                        if (!suppressErrorMessage) {
                            showStatusMessage('Failed to load products. Please try again later.', 'error');
                        }

                        // If we have products in memory, apply pending updates to them
                        if (allProducts.length > 0) {
                            allProducts = localProductCache.applyPendingUpdates(allProducts);
                            renderProductsTable(allProducts);
                        }

                        return false; // Failed
                    }
                }

                /**
                 * Render the products table based on the provided products array
                 * @param {Array} products - Array of product objects to display
                 */
                function renderProductsTable(products) {
                    // Clear existing table rows
                    productsTableBody.innerHTML = '';

                    // Populate table with products
                    for (const product of products) {
                        const row = document.createElement('tr');

                        row.innerHTML = `
                            <td>${product.item_code}</td>
                            <td>${product.room_name}</td>
                            <td title="${product.item_name}" class="tooltip-cell">${product.item_name}</td>
                            <td>${product.car_brand || 'N/A'}</td>
                            <td>${product.car_model || 'N/A'}</td>
                            <td>${capitalizeWords(product.product_category.replace(/_/g, ' '))}</td>
                            <td>${product.available_stock}</td>
                            <td>${product.low_stock_threshold || 5}</td>
                            <td>
                                <span style="display:inline-block;width:20px;height:20px;background-color:${product.colour_tape};border-radius:50%;border:1px solid #ddd;">
                                </span>
                            </td>
                            <td>${product.location || 'N/A'}</td>
                            <td>R ${Math.round(parseFloat(product.unit_retail_price)).toLocaleString()}</td>
                            <td>R ${Math.round(parseFloat(product.wholesale_price)).toLocaleString()}</td>
                            <td>R ${Math.round(parseFloat(product.unit_cost)).toLocaleString()}</td>
                            <td>R ${Math.round(parseFloat(product.profit)).toLocaleString()}</td>
                            <td>${product.supplier_code || 'N/A'}</td>
                            <td title="${product.additional_comments || 'None'}" class="tooltip-cell">${product.additional_comments || 'None'}</td>
                            <td class="actions-button">
                                <button class="edit-button" data-item-code="${product.item_code}" data-room-id="${product.room_id}" aria-label="Edit Product">&#9998;</button>
                                <button class="delete-button" data-item-code="${product.item_code}" data-room-id="${product.room_id}" aria-label="Delete Product">&#10006;</button>
                            </td>
                        `;
                        productsTableBody.appendChild(row);
                    }

                    // ALSO render the mobile card view:
                    renderProductsCards(products);
                }

                /**
                 * Populate filter dropdowns
                 */
                function populateFilters() {
                    populateFilterOptions(filterCarBrand, 'car_brand');
                    populateFilterOptions(filterProductCategory, 'product_category');
                    populateFilterOptions(filterCarModel, 'car_model');
                    populateFilterOptions(filterRoom, 'room_name');
                    populateColourTapeFilter();
                }

                /**
                 * Helper function to populate a single filter dropdown
                 * @param {HTMLElement} selectElement
                 * @param {string} key
                 */
                function populateFilterOptions(selectElement, key) {
                    // Clear existing options except the first one
                    while (selectElement.options.length > 1) {
                        selectElement.remove(1);
                    }

                    // Special handling for product_category
                    if (key === 'product_category') {
                        // Get unique category IDs from products
                        const uniqueCategoryIds = [...new Set(allProducts.map(p => p[key]).filter(Boolean))];

                        // For each category in our categories array that is used in products
                        categories.filter(cat => uniqueCategoryIds.includes(cat.id)).forEach(category => {
                            const option = document.createElement('option');
                            option.value = category.id;
                            option.textContent = category.name;
                            selectElement.appendChild(option);
                        });
                    } else {
                        // Standard handling for other filters
                        const uniqueValues = [...new Set(allProducts.map(p => p[key]).filter(Boolean))].sort();
                        uniqueValues.forEach(value => {
                            const option = document.createElement('option');
                            option.value = value;
                            option.textContent = capitalizeWords(value.replace(/_/g, ' '));
                            selectElement.appendChild(option);
                        });
                    }
                }

                /**
                 * Function to populate the Colour Tape filter with color swatches
                 */
                function populateColourTapeFilter() {
                    const uniqueColours = [...new Set(allProducts.map(p => p.colour_tape).filter(Boolean))].sort();
                    uniqueColours.forEach(color => {
                        const option = document.createElement('option');
                        option.value = color;
                        option.textContent = capitalizeWords(color);
                        // Set background/foreground for visibility
                        option.style.backgroundColor = color;
                        option.style.color = getContrastYIQ(color);
                        filterColourTape.appendChild(option);
                    });
                }

                /**
                 * Function to handle filter changes
                 */
                function handleFilters() {
                    const brand = filterCarBrand.value.toLowerCase();
                    const category = filterProductCategory.value.toLowerCase();
                    const model = filterCarModel.value.toLowerCase();
                    const room = filterRoom.value.toLowerCase();
                    const colour = filterColourTape.value.toLowerCase();

                    const query = searchInput.value.trim().toLowerCase();

                    const filtered = allProducts.filter(product => {
                        const matchesBrand = brand ? product.car_brand.toLowerCase() === brand : true;
                        const matchesCategory = category ? product.product_category.toLowerCase() === category : true;
                        const matchesModel = model ? product.car_model.toLowerCase() === model : true;
                        const matchesRoom = room ? product.room_name.toLowerCase() === room : true;
                        const matchesColour = colour ? product.colour_tape.toLowerCase() === colour : true;
                        const matchesSearch = product.item_name.toLowerCase().includes(query);
                        return matchesBrand && matchesCategory && matchesModel && matchesRoom && matchesColour && matchesSearch;
                    });

                    // Re-render with filtered results
                    renderProductsTable(filtered);
                }

                /**
                 * Handle Add Product form submission
                 */
                addProductForm.addEventListener('submit', async function(e) {
                    e.preventDefault();

                    // Collect form data
                    const formData = {
                        item_code: document.getElementById('itemCode').value.trim(),
                        room_id: parseInt(document.getElementById('room').value),
                        item_name: document.getElementById('itemName').value.trim(),
                        car_brand: document.getElementById('carBrand').value.trim(),
                        car_model: document.getElementById('carModel').value.trim(),
                        unit_retail_price: parseFloat(document.getElementById('unitRetailPrice').value),
                        wholesale_price: parseFloat(document.getElementById('wholesalePrice').value),
                        unit_cost: parseFloat(document.getElementById('unitCost').value),
                        supplier_code: document.getElementById('supplierCode').value.trim(),
                        available_stock: parseInt(document.getElementById('availableStock').value),
                        low_stock_threshold: parseInt(document.getElementById('lowStockThreshold').value) || 5,
                        min_order_quantity: parseInt(document.getElementById('minOrderQuantity').value) || 1,
                        location: document.getElementById('location').value.trim(),
                        colour_tape: document.getElementById('colourTape').value,
                        product_category: document.getElementById('productCategory').value,
                        additional_comments: document.getElementById('additionalComments').value.trim()
                    };

                    // Basic front-end checks, replaced alerts with toast-like messages:
                    if (!formData.item_code) {
                        showStatusMessage('Please enter an item code.', 'error');
                        return;
                    }
                    if (isNaN(formData.room_id)) {
                        showStatusMessage('Please select a valid room.', 'error');
                        return;
                    }
                    if (!formData.product_category) {
                        showStatusMessage('Please select a product category.', 'error');
                        return;
                    }

                    // Send POST request
                    try {
                        const response = await fetch(`${API_URL}/products`, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify(formData)
                        });

                        if (response.ok) {
                            await response.json();
                            showStatusMessage('Product added successfully!', 'success');
                            addProductForm.reset();
                            colourTape.value = '#000000';
                            colourTapeText.value = '#000000';
                            closeAddModal();
                            fetchProducts(); // Refresh the list
                        } else {
                            const errorData = await response.json();
                            showStatusMessage(`Error: ${errorData.message}`, 'error');
                        }
                    } catch (error) {
                        console.error('Error adding product:', error);
                        showStatusMessage('An error occurred while adding the product.', 'error');
                    }
                });

                /**
                 * Handle Edit Product form submission
                 */
                editProductForm.addEventListener('submit', async function(e) {
                    e.preventDefault();

                    const item_code = document.getElementById('editItemCode').value;
                    const formData = {
                        room_id: parseInt(document.getElementById('editRoom').value),
                        item_name: document.getElementById('editItemName').value.trim(),
                        car_brand: document.getElementById('editCarBrand').value.trim(),
                        car_model: document.getElementById('editCarModel').value.trim(),
                        unit_retail_price: parseFloat(document.getElementById('editUnitRetailPrice').value),
                        wholesale_price: parseFloat(document.getElementById('editWholesalePrice').value),
                        unit_cost: parseFloat(document.getElementById('editUnitCost').value),
                        supplier_code: document.getElementById('editSupplierCode').value.trim(),
                        available_stock: parseInt(document.getElementById('editAvailableStock').value),
                        low_stock_threshold: parseInt(document.getElementById('editLowStockThreshold').value) || 5,
                        min_order_quantity: Math.max(1, parseInt(document.getElementById('editMinOrderQuantity').value) || 1), // Ensure it's a number and at least 1
                        location: document.getElementById('editLocation').value.trim(),
                        colour_tape: document.getElementById('editColourTape').value,
                        product_category: document.getElementById('editProductCategory').value,
                        additional_comments: document.getElementById('editAdditionalComments').value.trim()
                    };

                    // Replaced alerts with toast-like messages:
                    if (isNaN(formData.room_id)) {
                        showStatusMessage('Please select a valid room.', 'error');
                        return;
                    }
                    if (!formData.product_category) {
                        showStatusMessage('Please select a product category.', 'error');
                        return;
                    }

                    // Send PUT request
                    try {
                        console.log('Sending update with formData:', formData);

                        // Try new API first (with original_room_id)
                        let url = `${API_URL}/products/${encodeURIComponent(item_code)}`;
                        if (originalRoomId) {
                            url += `?original_room_id=${originalRoomId}`;
                        }

                        const response = await fetch(url, {
                            method: 'PUT',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify(formData)
                        });

                        if (response.ok) {
                            const updatedProduct = await response.json();
                            console.log('Updated product from server:', updatedProduct);

                            // Remove from local cache if it was there
                            localProductCache.removePendingUpdate(item_code);

                            // Check if room was changed and show appropriate message
                            const roomChanged = originalRoomId && formData.room_id.toString() !== originalRoomId;
                            if (roomChanged) {
                                const newRoomSelect = document.getElementById('editRoom');
                                const newRoomName = newRoomSelect.options[newRoomSelect.selectedIndex].text;
                                showStatusMessage(`Product moved to ${newRoomName} successfully!`, 'success');
                            } else {
                                showStatusMessage('Product updated successfully!', 'success');
                            }

                            editProductForm.reset();
                            editColourTape.value = '#000000';
                            editColourTapeText.value = '#000000';
                            closeEditModal();
                            fetchProducts();
                        } else {
                            const errorData = await response.json();
                            showStatusMessage(`Error: ${errorData.message}`, 'error');
                        }
                    } catch (error) {
                        console.error('Error updating product:', error);

                        // Save the update to local cache
                        localProductCache.saveUpdate(item_code, formData);

                        // Update the product in memory
                        const productIndex = allProducts.findIndex(p => p.item_code === item_code);
                        if (productIndex !== -1) {
                            // Calculate profit
                            const profit = parseFloat(formData.unit_retail_price) - parseFloat(formData.unit_cost);

                            // Update the product in memory
                            allProducts[productIndex] = {
                                ...allProducts[productIndex],
                                ...formData,
                                profit: profit
                            };

                            // Re-render the table
                            renderProductsTable(allProducts);
                        }

                        showStatusMessage('Database connection failed. Changes saved locally and will be applied when connection is restored.', 'warning');
                        editProductForm.reset();
                        editColourTape.value = '#000000';
                        editColourTapeText.value = '#000000';
                        closeEditModal();
                    }
                });

                /**
                 * Sync color input with text input (Add Form)
                 */
                if (colourTape && colourTapeText) {
                    colourTape.addEventListener('input', () => { colourTapeText.value = colourTape.value; });
                    colourTapeText.addEventListener('input', () => {
                        if (/^#[0-9A-Fa-f]{6}$/.test(colourTapeText.value)) {
                            colourTape.value = colourTapeText.value;
                        }
                    });
                }

                /**
                 * Sync color input with text input (Edit Form)
                 */
                if (editColourTape && editColourTapeText) {
                    editColourTape.addEventListener('input', () => { editColourTapeText.value = editColourTape.value; });
                    editColourTapeText.addEventListener('input', () => {
                        if (/^#[0-9A-Fa-f]{6}$/.test(editColourTapeText.value)) {
                            editColourTape.value = editColourTapeText.value;
                        }
                    });
                }

                /**
                 * Event listeners to open/close modals
                 */
                addProductBlock.addEventListener('click', openAddModal);
                closePopup.addEventListener('click', closeAddModal);
                closeEditPopup.addEventListener('click', closeEditModal);

                // Add event listener for room change in edit modal
                let originalRoomId = null;
                document.getElementById('editRoom').addEventListener('change', function(e) {
                    const selectedRoomId = e.target.value;
                    const selectedRoomText = e.target.options[e.target.selectedIndex].text;

                    if (originalRoomId && selectedRoomId && selectedRoomId !== originalRoomId) {
                        // Show visual feedback that the product will be moved
                        e.target.style.backgroundColor = '#fff3cd';
                        e.target.style.borderColor = '#ffc107';

                        // Add a temporary message
                        let moveMessage = document.getElementById('roomMoveMessage');
                        if (!moveMessage) {
                            moveMessage = document.createElement('div');
                            moveMessage.id = 'roomMoveMessage';
                            moveMessage.style.cssText = 'color: #856404; background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 8px; border-radius: 4px; margin-top: 5px; font-size: 0.9em;';
                            e.target.parentNode.appendChild(moveMessage);
                        }
                        moveMessage.textContent = `Product will be moved to: ${selectedRoomText}`;
                    } else {
                        // Reset styling if back to original room
                        e.target.style.backgroundColor = '#f8f9fa';
                        e.target.style.borderColor = '#007bff';
                        const moveMessage = document.getElementById('roomMoveMessage');
                        if (moveMessage) {
                            moveMessage.remove();
                        }
                    }
                });

                // Close modals by clicking outside
                popupOverlay.addEventListener('click', function(e) {
                    if (e.target === popupOverlay) {
                        closeAddModal();
                    }
                });
                editProductOverlay.addEventListener('click', function(e) {
                    if (e.target === editProductOverlay) {
                        closeEditModal();
                    }
                });

                /**
                 * Table event delegation for Edit/Delete
                 */
                productsTableBody.addEventListener('click', async function(e) {
                    if (e.target.classList.contains('edit-button')) {
                        const item_code = e.target.getAttribute('data-item-code');
                        const room_id = e.target.getAttribute('data-room-id');
                        try {
                            let response, product;

                            // Try new API first (with room_id)
                            if (room_id) {
                                response = await fetch(`${API_URL}/products/${encodeURIComponent(item_code)}?room_id=${room_id}`);
                            } else {
                                // Fallback to old API
                                response = await fetch(`${API_URL}/products/${encodeURIComponent(item_code)}`);
                            }

                            if (!response.ok) {
                                throw new Error('Failed to fetch product details');
                            }

                            product = await response.json();

                            // Handle both single product (new API with room_id) and array (old API or new API without room_id)
                            if (Array.isArray(product)) {
                                if (product.length === 0) {
                                    throw new Error('Product not found');
                                }
                                // If multiple products, find the one matching the room_id
                                if (room_id) {
                                    const matchingProduct = product.find(p => p.room_id.toString() === room_id);
                                    product = matchingProduct || product[0];
                                } else {
                                    product = product[0]; // Take the first one
                                }
                            }

                            console.log('Product data from server:', product);
                            openEditModal(product);
                        } catch (error) {
                            console.error('Error fetching product details:', error);
                            showStatusMessage('Failed to load product details.', 'error');
                        }
                    }

                    if (e.target.classList.contains('delete-button')) {
                        const item_code = e.target.getAttribute('data-item-code');
                        const room_id = e.target.getAttribute('data-room-id');
                        showConfirmationToast(
                            'Are you sure you want to delete this product?',
                            async () => {
                                try {
                                    const response = await fetch(`${API_URL}/products/${encodeURIComponent(item_code)}?room_id=${room_id}`, {
                                        method: 'DELETE'
                                    });
                                    if (response.ok) {
                                        showStatusMessage('Product deleted successfully!', 'success');
                                        fetchProducts();
                                    } else {
                                        const errorData = await response.json();
                                        showStatusMessage(`Error: ${errorData.message}`, 'error');
                                    }
                                } catch (error) {
                                    console.error('Error deleting product:', error);
                                    showStatusMessage('An error occurred while deleting the product.', 'error');
                                }
                            }
                        );
                    }
                });

                /**
                 * Card event delegation for Edit/Delete (mobile)
                 */
                productsCardsContainer.addEventListener('click', async function(e) {
                    if (e.target.classList.contains('edit-button')) {
                        const item_code = e.target.getAttribute('data-item-code');
                        const room_id = e.target.getAttribute('data-room-id');
                        try {
                            let response, product;

                            // Try new API first (with room_id)
                            if (room_id) {
                                response = await fetch(`${API_URL}/products/${encodeURIComponent(item_code)}?room_id=${room_id}`);
                            } else {
                                // Fallback to old API
                                response = await fetch(`${API_URL}/products/${encodeURIComponent(item_code)}`);
                            }

                            if (!response.ok) {
                                throw new Error('Failed to fetch product details');
                            }

                            product = await response.json();

                            // Handle both single product (new API with room_id) and array (old API or new API without room_id)
                            if (Array.isArray(product)) {
                                if (product.length === 0) {
                                    throw new Error('Product not found');
                                }
                                // If multiple products, find the one matching the room_id
                                if (room_id) {
                                    const matchingProduct = product.find(p => p.room_id.toString() === room_id);
                                    product = matchingProduct || product[0];
                                } else {
                                    product = product[0]; // Take the first one
                                }
                            }

                            openEditModal(product);
                        } catch (error) {
                            console.error('Error fetching product details:', error);
                            showStatusMessage('Failed to load product details.', 'error');
                        }
                    }

                    if (e.target.classList.contains('delete-button')) {
                        const item_code = e.target.getAttribute('data-item-code');
                        const room_id = e.target.getAttribute('data-room-id');
                        showConfirmationToast(
                            'Are you sure you want to delete this product?',
                            async () => {
                                try {
                                    const response = await fetch(`${API_URL}/products/${encodeURIComponent(item_code)}?room_id=${room_id}`, {
                                        method: 'DELETE'
                                    });
                                    if (response.ok) {
                                        showStatusMessage('Product deleted successfully!', 'success');
                                        fetchProducts();
                                    } else {
                                        const errorData = await response.json();
                                        showStatusMessage(`Error: ${errorData.message}`, 'error');
                                    }
                                } catch (error) {
                                    console.error('Error deleting product:', error);
                                    showStatusMessage('An error occurred while deleting the product.', 'error');
                                }
                            }
                        );
                    }
                });

                /**
                 * Handle search input
                 */
                searchInput.addEventListener('input', function() {
                    handleFilters();
                });

                /**
                 * Filter change listeners
                 */
                filterCarBrand.addEventListener('change', handleFilters);
                filterProductCategory.addEventListener('change', handleFilters);
                filterCarModel.addEventListener('change', handleFilters);
                filterRoom.addEventListener('change', handleFilters);
                filterColourTape.addEventListener('change', handleFilters);

                /**
                 * Categories Modal Functions
                 */
                function openCategoriesModal() {
                    categoriesOverlay.style.display = 'flex';
                    categoriesOverlay.setAttribute('aria-hidden', 'false');
                    renderCategoriesTable();
                }

                function closeCategoriesModal() {
                    categoriesOverlay.style.display = 'none';
                    categoriesOverlay.setAttribute('aria-hidden', 'true');
                    addCategoryForm.reset();
                }

                function renderCategoriesTable() {
                    categoriesTableBody.innerHTML = '';

                    categories.forEach((category, index) => {
                        const row = document.createElement('tr');

                        // Category name cell
                        const nameCell = document.createElement('td');
                        nameCell.textContent = category.name;
                        row.appendChild(nameCell);

                        // Category value cell
                        const valueCell = document.createElement('td');
                        valueCell.textContent = category.id;
                        row.appendChild(valueCell);

                        // Actions cell
                        const actionsCell = document.createElement('td');
                        actionsCell.className = 'actions-button';

                        // Edit button
                        const editButton = document.createElement('button');
                        editButton.className = 'edit-button';
                        editButton.innerHTML = '&#9998;';
                        editButton.setAttribute('data-index', index);
                        editButton.addEventListener('click', () => editCategory(index));
                        actionsCell.appendChild(editButton);

                        // Delete button
                        const deleteButton = document.createElement('button');
                        deleteButton.className = 'delete-button';
                        deleteButton.innerHTML = '&#10006;';
                        deleteButton.setAttribute('data-index', index);
                        deleteButton.addEventListener('click', () => deleteCategory(index));
                        actionsCell.appendChild(deleteButton);

                        row.appendChild(actionsCell);
                        categoriesTableBody.appendChild(row);
                    });

                    // Update category dropdowns
                    updateCategoryDropdowns();
                }

                async function handleAddCategory(e) {
                    e.preventDefault();
                    const categoryName = document.getElementById('categoryName').value.trim();

                    if (!categoryName) {
                        showStatusMessage('Please enter a category name.', 'error');
                        return;
                    }

                    // Generate an ID from the name (lowercase with underscores)
                    const categoryId = categoryName.toLowerCase().replace(/\s+/g, '_');

                    // Check if category already exists
                    if (categories.some(cat => cat.id === categoryId)) {
                        showStatusMessage('A category with this name already exists.', 'error');
                        return;
                    }

                    try {
                        // Save the new category to the backend
                        await fetch(`${API_URL}/categories`, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                category_id: categoryId,
                                name: categoryName
                            })
                        });

                        // Refresh categories from the backend
                        await fetchCategories();

                        // Update the table
                        renderCategoriesTable();

                        // Clear the form
                        addCategoryForm.reset();

                        showStatusMessage('Category added successfully!', 'success');
                    } catch (error) {
                        console.error('Error adding category:', error);
                        showStatusMessage(`Failed to add category: ${error.message}`, 'error');
                    }
                }

                /**
                 * Handle adding a new car brand
                 */
                async function handleAddCarBrand(e) {
                    e.preventDefault();
                    const brandName = document.getElementById('brandName').value.trim();

                    if (!brandName) {
                        showStatusMessage('Please enter a brand name.', 'error');
                        return;
                    }

                    // Generate an ID from the name (lowercase with underscores)
                    const brandId = brandName.toLowerCase().replace(/\s+/g, '_');

                    // Check if brand already exists
                    if (carBrands.some(brand => brand.id === brandId)) {
                        showStatusMessage('A car brand with this name already exists.', 'error');
                        return;
                    }

                    try {
                        // Save the new brand to the backend
                        await fetch(`${API_URL}/car-brands`, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                brand_id: brandId,
                                name: brandName
                            })
                        });

                        // Refresh brands from the backend
                        await fetchCarBrands();

                        // Update the table
                        renderCarBrandsTable();

                        // Clear the form
                        addCarBrandForm.reset();

                        showStatusMessage('Car brand added successfully!', 'success');
                    } catch (error) {
                        console.error('Error adding car brand:', error);
                        showStatusMessage(`Failed to add car brand: ${error.message}`, 'error');
                    }
                }

                /**
                 * Handle adding a new car model
                 */
                async function handleAddCarModel(e) {
                    e.preventDefault();
                    const modelName = document.getElementById('modelName').value.trim();
                    const brandId = document.getElementById('modelBrandId').value;

                    if (!modelName) {
                        showStatusMessage('Please enter a model name.', 'error');
                        return;
                    }

                    if (!brandId) {
                        showStatusMessage('Please select a brand.', 'error');
                        return;
                    }

                    // Generate an ID from the name (lowercase with underscores)
                    const modelId = modelName.toLowerCase().replace(/\s+/g, '_');

                    // Check if model already exists
                    if (carModels.some(model => model.id === modelId)) {
                        showStatusMessage('A car model with this name already exists.', 'error');
                        return;
                    }

                    try {
                        // Save the new model to the backend
                        await fetch(`${API_URL}/car-models`, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                model_id: modelId,
                                name: modelName,
                                brand_id: brandId
                            })
                        });

                        // Refresh models from the backend
                        await fetchCarModels();

                        // Update the table
                        renderCarModelsTable();

                        // Clear the form
                        addCarModelForm.reset();

                        showStatusMessage('Car model added successfully!', 'success');
                    } catch (error) {
                        console.error('Error adding car model:', error);
                        showStatusMessage(`Failed to add car model: ${error.message}`, 'error');
                    }
                }

                async function editCategory(index) {
                    const category = categories[index];

                    showPromptToast(
                        'Enter new name for category:',
                        category.name,
                        async (newName) => {
                            if (newName && newName.trim() !== '') {
                                try {
                                    // Update the category in the backend
                                    await fetch(`${API_URL}/categories/${category.id}`, {
                                        method: 'PUT',
                                        headers: { 'Content-Type': 'application/json' },
                                        body: JSON.stringify({ name: newName.trim() })
                                    });

                                    // Refresh categories from the backend
                                    await fetchCategories();

                                    // Update the table
                                    renderCategoriesTable();

                                    showStatusMessage('Category updated successfully!', 'success');
                                } catch (error) {
                                    console.error('Error updating category:', error);
                                    showStatusMessage(`Failed to update category: ${error.message}`, 'error');
                                }
                            }
                        }
                    );
                }

                /**
                 * Edit a car brand
                 */
                async function editCarBrand(index) {
                    const brand = carBrands[index];

                    showPromptToast(
                        'Enter new name for car brand:',
                        brand.name,
                        async (newName) => {
                            if (newName && newName.trim() !== '') {
                                try {
                                    // Update the brand in the backend
                                    await fetch(`${API_URL}/car-brands/${brand.id}`, {
                                        method: 'PUT',
                                        headers: { 'Content-Type': 'application/json' },
                                        body: JSON.stringify({ name: newName.trim() })
                                    });

                                    // Refresh brands from the backend
                                    await fetchCarBrands();

                                    // Update the table
                                    renderCarBrandsTable();

                                    showStatusMessage('Car brand updated successfully!', 'success');
                                } catch (error) {
                                    console.error('Error updating car brand:', error);
                                    showStatusMessage(`Failed to update car brand: ${error.message}`, 'error');
                                }
                            }
                        }
                    );
                }

                async function deleteCategory(index) {
                    const category = categories[index];

                    // Check if any products are using this category
                    const productsUsingCategory = allProducts.filter(product =>
                        product.product_category === category.id
                    );

                    if (productsUsingCategory.length > 0) {
                        showStatusMessage(`Cannot delete category. It is used by ${productsUsingCategory.length} product(s).`, 'error');
                        return;
                    }

                    showConfirmationToast(
                        `Are you sure you want to delete the category "${category.name}"?`,
                        async () => {
                            try {
                                // Delete the category from the backend
                                await fetch(`${API_URL}/categories/${category.id}`, {
                                    method: 'DELETE'
                                });

                                // Refresh categories from the backend
                                await fetchCategories();

                                // Update the table
                                renderCategoriesTable();

                                showStatusMessage('Category deleted successfully!', 'success');
                            } catch (error) {
                                console.error('Error deleting category:', error);
                                showStatusMessage(`Failed to delete category: ${error.message}`, 'error');
                            }
                        }
                    );
                }

                /**
                 * Delete a car brand
                 */
                async function deleteCarBrand(index) {
                    const brand = carBrands[index];

                    // Check if any products are using this brand
                    const productsUsingBrand = allProducts.filter(product =>
                        product.car_brand === brand.name
                    );

                    if (productsUsingBrand.length > 0) {
                        showStatusMessage(`Cannot delete car brand. It is used by ${productsUsingBrand.length} product(s).`, 'error');
                        return;
                    }

                    // Check if any models are using this brand
                    const modelsUsingBrand = carModels.filter(model =>
                        model.brandId === brand.id
                    );

                    if (modelsUsingBrand.length > 0) {
                        showStatusMessage(`Cannot delete car brand. It is used by ${modelsUsingBrand.length} car model(s).`, 'error');
                        return;
                    }

                    showConfirmationToast(
                        `Are you sure you want to delete the car brand "${brand.name}"?`,
                        async () => {
                            try {
                                // Delete the brand from the backend
                                await fetch(`${API_URL}/car-brands/${brand.id}`, {
                                    method: 'DELETE'
                                });

                                // Refresh brands from the backend
                                await fetchCarBrands();

                                // Update the table
                                renderCarBrandsTable();

                                showStatusMessage('Car brand deleted successfully!', 'success');
                            } catch (error) {
                                console.error('Error deleting car brand:', error);
                                showStatusMessage(`Failed to delete car brand: ${error.message}`, 'error');
                            }
                        }
                    );
                }

                /**
                 * Edit a car model
                 */
                async function editCarModel(modelId) {
                    // Find the model in our array
                    const model = carModels.find(m => m.id === modelId);
                    if (!model) {
                        showStatusMessage('Car model not found.', 'error');
                        return;
                    }

                    // Find the brand for this model
                    const brand = carBrands.find(b => b.id === model.brandId);
                    const brandName = brand ? brand.name : 'Unknown';

                    // Prompt for new name first
                    showPromptToast(
                        `Enter new name for car model (currently: ${model.name})`,
                        model.name,
                        (newName) => {
                            if (!newName || newName.trim() === '') return;

                            // Then prompt for new brand
                            const brandOptions = carBrands.map(b => `${b.id}: ${b.name}`).join('\n');
                            const promptMessage = `Select a brand ID for this model (currently: ${brandName}):\n${brandOptions}`;

                            showPromptToast(
                                promptMessage,
                                model.brandId,
                                async (newBrandId) => {
                                    if (!newBrandId) return;

                                    // Check if the brand exists
                                    if (!carBrands.some(b => b.id === newBrandId)) {
                                        showStatusMessage('Invalid brand ID selected.', 'error');
                                        return;
                                    }

                                    try {
                                        // Update the model in the backend
                                        await fetch(`${API_URL}/car-models/${modelId}`, {
                                            method: 'PUT',
                                            headers: { 'Content-Type': 'application/json' },
                                            body: JSON.stringify({
                                                name: newName.trim(),
                                                brand_id: newBrandId
                                            })
                                        });

                                        // Refresh models from the backend
                                        await fetchCarModels();

                                        // Update the table
                                        renderCarModelsTable();

                                        showStatusMessage('Car model updated successfully!', 'success');
                                    } catch (error) {
                                        console.error('Error updating car model:', error);
                                        showStatusMessage(`Failed to update car model: ${error.message}`, 'error');
                                    }
                                }
                            );
                        }
                    );
                }

                /**
                 * Delete a car model
                 */
                async function deleteCarModel(modelId) {
                    // Find the model in our array
                    const model = carModels.find(m => m.id === modelId);
                    if (!model) {
                        showStatusMessage('Car model not found.', 'error');
                        return;
                    }

                    // Check if any products are using this model
                    const productsUsingModel = allProducts.filter(product =>
                        product.car_model === model.name
                    );

                    if (productsUsingModel.length > 0) {
                        showStatusMessage(`Cannot delete car model. It is used by ${productsUsingModel.length} product(s).`, 'error');
                        return;
                    }

                    showConfirmationToast(
                        `Are you sure you want to delete the car model "${model.name}"?`,
                        async () => {
                            try {
                                // Delete the model from the backend
                                await fetch(`${API_URL}/car-models/${modelId}`, {
                                    method: 'DELETE'
                                });

                                // Refresh models from the backend
                                await fetchCarModels();

                                // Update the table
                                renderCarModelsTable();

                                showStatusMessage('Car model deleted successfully!', 'success');
                            } catch (error) {
                                console.error('Error deleting car model:', error);
                                showStatusMessage(`Failed to delete car model: ${error.message}`, 'error');
                            }
                        }
                    );
                }

                /**
                 * Fetch all categories from the backend
                 */
                async function fetchCategories() {
                    try {
                        const response = await fetch(`${API_URL}/categories`);
                        if (!response.ok) {
                            throw new Error('Failed to fetch categories');
                        }
                        const fetchedCategories = await response.json();

                        // Map the backend category structure to our frontend structure
                        categories = fetchedCategories.map(cat => ({
                            id: cat.category_id,
                            name: cat.name
                        }));

                        // Update category dropdowns
                        updateCategoryDropdowns();
                    } catch (error) {
                        console.error('Error fetching categories:', error);
                        showStatusMessage('Failed to load categories. Please try again later.', 'error');
                    }
                }

                /**
                 * Fetch all car brands from the backend
                 */
                async function fetchCarBrands() {
                    try {
                        const response = await fetch(`${API_URL}/car-brands`);
                        if (!response.ok) {
                            throw new Error('Failed to fetch car brands');
                        }
                        const fetchedBrands = await response.json();

                        // Map the backend brand structure to our frontend structure
                        carBrands = fetchedBrands.map(brand => ({
                            id: brand.brand_id,
                            name: brand.name
                        }));

                        // Update car brand dropdowns
                        updateCarBrandDropdowns();

                        // Update model brand dropdowns
                        updateModelBrandDropdowns();
                    } catch (error) {
                        console.error('Error fetching car brands:', error);
                        showStatusMessage('Failed to load car brands. Please try again later.', 'error');
                    }
                }

                /**
                 * Fetch all car models from the backend
                 */
                async function fetchCarModels() {
                    try {
                        const response = await fetch(`${API_URL}/car-models`);
                        if (!response.ok) {
                            throw new Error('Failed to fetch car models');
                        }
                        const fetchedModels = await response.json();

                        // Map the backend model structure to our frontend structure
                        carModels = fetchedModels.map(model => ({
                            id: model.model_id,
                            name: model.name,
                            brandId: model.brand_id
                        }));

                        // Update car model dropdowns
                        updateCarModelDropdowns();
                    } catch (error) {
                        console.error('Error fetching car models:', error);
                        showStatusMessage('Failed to load car models. Please try again later.', 'error');
                    }
                }

                /**
                 * Fetch car models for a specific brand
                 */
                async function fetchCarModelsByBrand(brandId) {
                    if (!brandId) {
                        return [];
                    }

                    try {
                        const response = await fetch(`${API_URL}/car-models/brand/${brandId}`);
                        if (!response.ok) {
                            throw new Error('Failed to fetch car models for brand');
                        }
                        const fetchedModels = await response.json();

                        return fetchedModels.map(model => ({
                            id: model.model_id,
                            name: model.name,
                            brandId: model.brand_id
                        }));
                    } catch (error) {
                        console.error(`Error fetching car models for brand ${brandId}:`, error);
                        showStatusMessage('Failed to load car models for this brand. Please try again later.', 'error');
                        return [];
                    }
                }

                /**
                 * Render the categories table
                 */
                function renderCategoriesTable() {
                    // Clear existing table rows
                    categoriesTableBody.innerHTML = '';

                    // Sort categories alphabetically by name
                    const sortedCategories = [...categories].sort((a, b) => a.name.localeCompare(b.name));

                    // Populate table with categories
                    sortedCategories.forEach((category, index) => {
                        const row = document.createElement('tr');

                        // Create cells
                        const nameCell = document.createElement('td');
                        nameCell.textContent = category.name;

                        const idCell = document.createElement('td');
                        idCell.textContent = category.id;

                        const actionsCell = document.createElement('td');

                        // Create edit button
                        const editButton = document.createElement('button');
                        editButton.className = 'edit-button';
                        editButton.innerHTML = '&#9998;';
                        editButton.setAttribute('aria-label', 'Edit Category');
                        editButton.onclick = function() { editCategory(index); };

                        // Create delete button
                        const deleteButton = document.createElement('button');
                        deleteButton.className = 'delete-button';
                        deleteButton.innerHTML = '&#10006;';
                        deleteButton.setAttribute('aria-label', 'Delete Category');
                        deleteButton.onclick = function() { deleteCategory(index); };

                        // Add buttons to actions cell
                        actionsCell.appendChild(editButton);
                        actionsCell.appendChild(deleteButton);

                        // Add cells to row
                        row.appendChild(nameCell);
                        row.appendChild(idCell);
                        row.appendChild(actionsCell);

                        // Add row to table body
                        categoriesTableBody.appendChild(row);
                    });
                }
                /**
                 * Render the car brands table
                 */
                function renderCarBrandsTable() {
                    // Clear existing table rows
                    carBrandsTableBody.innerHTML = '';

                    // Sort brands alphabetically by name
                    const sortedBrands = [...carBrands].sort((a, b) => a.name.localeCompare(b.name));

                    // Populate table with brands
                    sortedBrands.forEach((brand, index) => {
                        const row = document.createElement('tr');

                        // Create cells
                        const nameCell = document.createElement('td');
                        nameCell.textContent = brand.name;

                        const idCell = document.createElement('td');
                        idCell.textContent = brand.id;

                        const actionsCell = document.createElement('td');

                        // Create edit button
                        const editButton = document.createElement('button');
                        editButton.className = 'edit-button';
                        editButton.innerHTML = '&#9998;';
                        editButton.setAttribute('aria-label', 'Edit Car Brand');
                        editButton.onclick = function() { editCarBrand(index); };

                        // Create delete button
                        const deleteButton = document.createElement('button');
                        deleteButton.className = 'delete-button';
                        deleteButton.innerHTML = '&#10006;';
                        deleteButton.setAttribute('aria-label', 'Delete Car Brand');
                        deleteButton.onclick = function() { deleteCarBrand(index); };

                        // Add buttons to actions cell
                        actionsCell.appendChild(editButton);
                        actionsCell.appendChild(deleteButton);

                        // Add cells to row
                        row.appendChild(nameCell);
                        row.appendChild(idCell);
                        row.appendChild(actionsCell);

                        // Add row to table body
                        carBrandsTableBody.appendChild(row);
                    });
                }

                /**
                 * Render the car models table
                 */
                function renderCarModelsTable() {
                    // Clear existing table rows
                    carModelsTableBody.innerHTML = '';

                    // Get the selected brand filter
                    const filterBrandId = filterModelBrand.value;

                    // Filter models by brand if a brand is selected
                    let filteredModels = [...carModels];
                    if (filterBrandId) {
                        filteredModels = filteredModels.filter(model => model.brandId === filterBrandId);
                    }

                    // Sort models alphabetically by name
                    const sortedModels = filteredModels.sort((a, b) => a.name.localeCompare(b.name));

                    // Populate table with models
                    sortedModels.forEach((model, index) => {
                        const row = document.createElement('tr');

                        // Create cells
                        const nameCell = document.createElement('td');
                        nameCell.textContent = model.name;

                        // Find the brand name for this model
                        const brand = carBrands.find(b => b.id === model.brandId);
                        const brandName = brand ? brand.name : 'Unknown';

                        const brandCell = document.createElement('td');
                        brandCell.textContent = brandName;

                        const idCell = document.createElement('td');
                        idCell.textContent = model.id;

                        const actionsCell = document.createElement('td');

                        // Create edit button
                        const editButton = document.createElement('button');
                        editButton.className = 'edit-button';
                        editButton.innerHTML = '&#9998;';
                        editButton.setAttribute('aria-label', 'Edit Car Model');
                        editButton.onclick = function() { editCarModel(model.id); };

                        // Create delete button
                        const deleteButton = document.createElement('button');
                        deleteButton.className = 'delete-button';
                        deleteButton.innerHTML = '&#10006;';
                        deleteButton.setAttribute('aria-label', 'Delete Car Model');
                        deleteButton.onclick = function() { deleteCarModel(model.id); };

                        // Add buttons to actions cell
                        actionsCell.appendChild(editButton);
                        actionsCell.appendChild(deleteButton);

                        // Add cells to row
                        row.appendChild(nameCell);
                        row.appendChild(brandCell);
                        row.appendChild(idCell);
                        row.appendChild(actionsCell);

                        // Add row to table body
                        carModelsTableBody.appendChild(row);
                    });
                }

                /**
                 * Update the category dropdowns in the add and edit forms
                 */
                function updateCategoryDropdowns() {
                    // Update the category dropdowns in the add and edit forms
                    const productCategorySelect = document.getElementById('productCategory');
                    const editProductCategorySelect = document.getElementById('editProductCategory');

                    // Clear existing options except the first one
                    while (productCategorySelect.options.length > 1) {
                        productCategorySelect.remove(1);
                    }

                    while (editProductCategorySelect.options.length > 1) {
                        editProductCategorySelect.remove(1);
                    }

                    // Add options for each category
                    categories.forEach(category => {
                        const option = new Option(category.name, category.id);
                        const editOption = new Option(category.name, category.id);

                        productCategorySelect.add(option);
                        editProductCategorySelect.add(editOption);
                    });
                }

                /**
                 * Update the car brand dropdowns in the add and edit forms
                 */
                function updateCarBrandDropdowns() {
                    try {
                        // Update the car brand dropdowns in the add and edit forms
                        const carBrandSelect = document.getElementById('carBrand');
                        const editCarBrandSelect = document.getElementById('editCarBrand');

                        // Update each dropdown individually if it exists
                        if (carBrandSelect) {
                            updateSingleCarBrandDropdown(carBrandSelect, 'carBrand');
                        }

                        if (editCarBrandSelect) {
                            updateSingleCarBrandDropdown(editCarBrandSelect, 'editCarBrand');
                        }
                    } catch (error) {
                        console.error('Error updating car brand dropdowns:', error);
                    }
                }

                /**
                 * Update a single car brand dropdown
                 */
                function updateSingleCarBrandDropdown(selectElement, elementName) {
                    try {
                        // Store current value to restore it after updating options
                        const currentValue = selectElement.value;

                        // Clear existing options except the first one
                        while (selectElement.options.length > 1) {
                            selectElement.remove(1);
                        }

                        // Add options for each brand
                        carBrands.forEach(brand => {
                            const option = document.createElement('option');
                            option.value = brand.name;
                            option.textContent = brand.name;
                            selectElement.appendChild(option);
                        });

                        // Restore previously selected value if it still exists
                        if (currentValue) {
                            selectElement.value = currentValue;
                        }
                    } catch (error) {
                        console.error(`Error updating ${elementName} dropdown:`, error);
                    }
                }

                /**
                 * Update a single car model dropdown for a specific brand
                 */
                async function updateSingleCarModelDropdown(selectElement, elementName, brandName) {
                    try {
                        // Store current value to restore it after updating options
                        const currentValue = selectElement.value;

                        // Clear existing options except the first one
                        while (selectElement.options.length > 1) {
                            selectElement.remove(1);
                        }

                        if (brandName) {
                            // Find the brand ID from the name
                            const brand = carBrands.find(b => b.name === brandName);
                            if (brand) {
                                // Fetch models for this brand
                                const brandModels = await fetchCarModelsByBrand(brand.id);

                                // Add options for each model
                                brandModels.forEach(model => {
                                    const option = document.createElement('option');
                                    option.value = model.name;
                                    option.textContent = model.name;
                                    selectElement.appendChild(option);
                                });

                                // Restore previously selected value if it still exists
                                if (currentValue) {
                                    selectElement.value = currentValue;
                                }
                            }
                        }
                    } catch (error) {
                        console.error(`Error updating ${elementName} dropdown:`, error);
                    }
                }

                /**
                 * Update the model brand dropdowns in the add and filter forms
                 */
                function updateModelBrandDropdowns() {
                    try {
                        if (!modelBrandId || !filterModelBrand) {
                            console.error('Model brand select elements not found');
                            return;
                        }

                        // Store current values to restore them after updating options
                        const currentAddValue = modelBrandId.value;
                        const currentFilterValue = filterModelBrand.value;

                        // Clear existing options except the first one
                        while (modelBrandId.options.length > 1) {
                            modelBrandId.remove(1);
                        }

                        while (filterModelBrand.options.length > 1) {
                            filterModelBrand.remove(1);
                        }

                        // Add options for each brand
                        carBrands.forEach(brand => {
                            const addOption = document.createElement('option');
                            addOption.value = brand.id;
                            addOption.textContent = brand.name;

                            const filterOption = document.createElement('option');
                            filterOption.value = brand.id;
                            filterOption.textContent = brand.name;

                            modelBrandId.appendChild(addOption);
                            filterModelBrand.appendChild(filterOption);
                        });

                        // Restore previously selected values if they still exist
                        if (currentAddValue) {
                            modelBrandId.value = currentAddValue;
                        }
                        if (currentFilterValue) {
                            filterModelBrand.value = currentFilterValue;
                        }

                        console.log('Model brand dropdowns updated with', carBrands.length, 'brands');
                    } catch (error) {
                        console.error('Error updating model brand dropdowns:', error);
                    }
                }

                /**
                 * Update the car model dropdowns in the add and edit forms
                 */
                function updateCarModelDropdowns() {
                    try {
                        // Update the car model dropdowns in the add and edit forms
                        const carModelSelect = document.getElementById('carModel');
                        const editCarModelSelect = document.getElementById('editCarModel');

                        if (!carModelSelect || !editCarModelSelect) {
                            console.error('Car model select elements not found');
                            return;
                        }

                        // Store current values to restore them after updating options
                        const currentValue = carModelSelect.value;
                        const currentEditValue = editCarModelSelect.value;

                        // Clear existing options except the first one
                        while (carModelSelect.options.length > 1) {
                            carModelSelect.remove(1);
                        }

                        while (editCarModelSelect.options.length > 1) {
                            editCarModelSelect.remove(1);
                        }

                        // Add options for each model
                        carModels.forEach(model => {
                            const option = document.createElement('option');
                            option.value = model.name;
                            option.textContent = model.name;
                            option.dataset.brandId = model.brandId;

                            const editOption = document.createElement('option');
                            editOption.value = model.name;
                            editOption.textContent = model.name;
                            editOption.dataset.brandId = model.brandId;

                            carModelSelect.appendChild(option);
                            editCarModelSelect.appendChild(editOption);
                        });

                        // Restore previously selected values if they still exist
                        if (currentValue) {
                            carModelSelect.value = currentValue;
                        }
                        if (currentEditValue) {
                            editCarModelSelect.value = currentEditValue;
                        }

                        console.log('Car model dropdowns updated with', carModels.length, 'models');
                    } catch (error) {
                        console.error('Error updating car model dropdowns:', error);
                    }
                }

                /**
                 * Update car models based on selected brand
                 */
                // Flag to track if event listeners have been added
                let carBrandEventListenersAdded = false;

                async function updateCarModelsBySelectedBrand() {
                    try {
                        const carBrandSelect = document.getElementById('carBrand');
                        const carModelSelect = document.getElementById('carModel');
                        const editCarBrandSelect = document.getElementById('editCarBrand');
                        const editCarModelSelect = document.getElementById('editCarModel');

                        if (!carBrandSelect || !carModelSelect || !editCarBrandSelect || !editCarModelSelect) {
                            console.error('Car brand or model select elements not found');
                            return;
                        }

                        // Add event listeners only once
                        if (!carBrandEventListenersAdded) {
                            // Add event listeners to update models when brand changes
                            carBrandSelect.addEventListener('change', async function() {
                            const selectedBrand = this.value;
                            if (!selectedBrand) {
                                // Clear model options if no brand selected
                                while (carModelSelect.options.length > 1) {
                                    carModelSelect.remove(1);
                                }
                                return;
                            }

                            // Find the brand ID from the name
                            const brand = carBrands.find(b => b.name === selectedBrand);
                            if (!brand) return;

                            // Fetch models for this brand
                            const brandModels = await fetchCarModelsByBrand(brand.id);

                            // Clear existing options except the first one
                            while (carModelSelect.options.length > 1) {
                                carModelSelect.remove(1);
                            }

                            // Add options for each model
                            brandModels.forEach(model => {
                                const option = document.createElement('option');
                                option.value = model.name;
                                option.textContent = model.name;
                                carModelSelect.appendChild(option);
                            });
                        });

                        // Same for edit form
                        editCarBrandSelect.addEventListener('change', async function() {
                            const selectedBrand = this.value;
                            if (!selectedBrand) {
                                // Clear model options if no brand selected
                                while (editCarModelSelect.options.length > 1) {
                                    editCarModelSelect.remove(1);
                                }
                                return;
                            }

                            // Find the brand ID from the name
                            const brand = carBrands.find(b => b.name === selectedBrand);
                            if (!brand) return;

                            // Fetch models for this brand
                            const brandModels = await fetchCarModelsByBrand(brand.id);

                            // Clear existing options except the first one
                            while (editCarModelSelect.options.length > 1) {
                                editCarModelSelect.remove(1);
                            }

                            // Add options for each model
                            brandModels.forEach(model => {
                                const option = document.createElement('option');
                                option.value = model.name;
                                option.textContent = model.name;
                                editCarModelSelect.appendChild(option);
                            });
                        });

                        // Mark that event listeners have been added
                        carBrandEventListenersAdded = true;
                        }
                    } catch (error) {
                        console.error('Error updating car models by selected brand:', error);
                    }
                }

                /**
                 * Open the categories modal
                 */
                function openCategoriesModal() {
                    categoriesOverlay.style.display = 'flex';
                    categoriesOverlay.setAttribute('aria-hidden', 'false');
                    renderCategoriesTable();
                }

                /**
                 * Close the categories modal
                 */
                function closeCategoriesModal() {
                    categoriesOverlay.style.display = 'none';
                    categoriesOverlay.setAttribute('aria-hidden', 'true');
                    addCategoryForm.reset();
                }

                /**
                 * Open the car brands modal
                 */
                function openCarBrandsModal() {
                    carBrandsOverlay.style.display = 'flex';
                    carBrandsOverlay.setAttribute('aria-hidden', 'false');
                    renderCarBrandsTable();
                }

                /**
                 * Close the car brands modal
                 */
                function closeCarBrandsModal() {
                    carBrandsOverlay.style.display = 'none';
                    carBrandsOverlay.setAttribute('aria-hidden', 'true');
                    addCarBrandForm.reset();
                }

                /**
                 * Open the car models modal
                 */
                function openCarModelsModal() {
                    carModelsOverlay.style.display = 'flex';
                    carModelsOverlay.setAttribute('aria-hidden', 'false');
                    renderCarModelsTable();
                }

                /**
                 * Close the car models modal
                 */
                function closeCarModelsModal() {
                    carModelsOverlay.style.display = 'none';
                    carModelsOverlay.setAttribute('aria-hidden', 'true');
                    addCarModelForm.reset();
                }

                // Event listeners for categories
                manageCategoriesBtn.addEventListener('click', openCategoriesModal);
                closeCategoriesPopup.addEventListener('click', closeCategoriesModal);
                addCategoryForm.addEventListener('submit', handleAddCategory);

                // Event listeners for car brands
                manageCarBrandsBtn.addEventListener('click', openCarBrandsModal);
                closeCarBrandsPopup.addEventListener('click', closeCarBrandsModal);
                addCarBrandForm.addEventListener('submit', handleAddCarBrand);

                // Event listeners for edit car brand
                if (typeof closeEditCarBrandPopup !== 'undefined' && closeEditCarBrandPopup !== null) {
                    closeEditCarBrandPopup.addEventListener('click', closeEditCarBrandModal);
                }
                if (typeof editCarBrandForm !== 'undefined' && editCarBrandForm !== null) {
                    editCarBrandForm.addEventListener('submit', handleEditCarBrand);
                }

                // Close edit car brand modal when clicking outside
                if (typeof editCarBrandOverlay !== 'undefined' && editCarBrandOverlay !== null) {
                    editCarBrandOverlay.addEventListener('click', function(e) {
                        if (e.target === editCarBrandOverlay) {
                            closeEditCarBrandModal();
                        }
                    });
                }

                // Event listeners for car models
                manageCarModelsBtn.addEventListener('click', openCarModelsModal);
                closeCarModelsPopup.addEventListener('click', closeCarModelsModal);
                addCarModelForm.addEventListener('submit', handleAddCarModel);

                // Add event listener for filter brand dropdown
                filterModelBrand.addEventListener('change', renderCarModelsTable);

                // Initial load - first fetch data
                async function initializeData() {
                    try {
                        // Check if we're coming from the low-stock page
                        const urlParams = new URLSearchParams(window.location.search);
                        const isFromLowStock = urlParams.has('edit_item');

                        // If coming from low-stock page, clear any existing status messages
                        if (isFromLowStock) {
                            document.getElementById('statusMessage').style.display = 'none';
                        }

                        // Create a modified version of fetchCategories that doesn't show errors
                        const fetchCategoriesSilent = async () => {
                            try {
                                const response = await fetch(`${API_URL}/categories`);
                                if (!response.ok) {
                                    throw new Error('Failed to fetch categories');
                                }
                                const fetchedCategories = await response.json();
                                categories = fetchedCategories.map(cat => ({
                                    id: cat.category_id,
                                    name: cat.name
                                }));
                                updateCategoryDropdowns();
                            } catch (error) {
                                console.error('Error fetching categories:', error);
                                // Don't show error message
                            }
                        };

                        // Create a modified version of fetchCarBrands that doesn't show errors
                        const fetchCarBrandsSilent = async () => {
                            try {
                                const response = await fetch(`${API_URL}/car-brands`);
                                if (!response.ok) {
                                    throw new Error('Failed to fetch car brands');
                                }
                                const fetchedBrands = await response.json();
                                carBrands = fetchedBrands.map(brand => ({
                                    id: brand.brand_id,
                                    name: brand.name
                                }));
                                updateCarBrandDropdowns();
                                updateModelBrandDropdowns();
                            } catch (error) {
                                console.error('Error fetching car brands:', error);
                                // Don't show error message
                            }
                        };

                        // Create a modified version of fetchCarModels that doesn't show errors
                        const fetchCarModelsSilent = async () => {
                            try {
                                const response = await fetch(`${API_URL}/car-models`);
                                if (!response.ok) {
                                    throw new Error('Failed to fetch car models');
                                }
                                const fetchedModels = await response.json();
                                carModels = fetchedModels.map(model => ({
                                    id: model.model_id,
                                    name: model.name,
                                    brandId: model.brand_id
                                }));
                                updateCarModelDropdowns();
                            } catch (error) {
                                console.error('Error fetching car models:', error);
                                // Don't show error message
                            }
                        };

                        // Create a modified version of fetchRooms that doesn't show errors
                        const fetchRoomsSilent = async () => {
                            try {
                                const response = await fetch(`${API_URL}/rooms`);
                                if (!response.ok) {
                                    throw new Error('Failed to fetch rooms');
                                }
                                const fetchedRooms = await response.json();
                                rooms = fetchedRooms;
                                updateRoomDropdowns();
                            } catch (error) {
                                console.error('Error fetching rooms:', error);
                                // Don't show error message
                            }
                        };

                        // Load all required data first - use silent versions if coming from low-stock page
                        if (isFromLowStock) {
                            await Promise.all([
                                fetchCategoriesSilent(),
                                fetchCarBrandsSilent(),
                                fetchCarModelsSilent(),
                                fetchRoomsSilent()
                            ]);
                        } else {
                            await Promise.all([
                                fetchCategories(),
                                fetchCarBrands(),
                                fetchCarModels(),
                                fetchRooms()
                            ]);
                        }

                        // Then load products - suppress error message if coming from low-stock page
                        await fetchProducts(isFromLowStock);

                        // After all data is loaded, check if we need to open the edit popup
                        checkForEditItemParam();
                    } catch (error) {
                        console.error('Error during initialization:', error);
                    }
                }

                // Start the initialization process
                initializeData();

                // Wait a bit to make sure all DOM elements are properly loaded and initialized
                setTimeout(() => {
                    // Re-check for the edit car brand elements
                    const editCarBrandPopupClose = document.getElementById('closeEditCarBrandPopup');
                    if (editCarBrandPopupClose) {
                        editCarBrandPopupClose.addEventListener('click', closeEditCarBrandModal);
                    } else {
                        console.warn('Edit car brand close button not found');
                    }

                    const editBrandForm = document.getElementById('editCarBrandForm');
                    if (editBrandForm) {
                        editBrandForm.addEventListener('submit', handleEditCarBrand);
                    } else {
                        console.warn('Edit car brand form not found');
                    }

                    const editBrandOverlay = document.getElementById('editCarBrandOverlay');
                    if (editBrandOverlay) {
                        editBrandOverlay.addEventListener('click', function(e) {
                            if (e.target === editBrandOverlay) {
                                closeEditCarBrandModal();
                            }
                        });
                    } else {
                        console.warn('Edit car brand overlay not found');
                    }

                    // Set up car model filtering event listeners after DOM is ready
                    updateCarModelsBySelectedBrand();
                }, 500);
            });
        </script>
    </div>
</body>
</html>
