<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <!-- Add performance-focused meta tags -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#ffffff">
    <meta name="format-detection" content="telephone=no">
    <title>Shans Admin Dashboard</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../Assets/performance.css">
    <script src="../Assets/performance.js"></script>
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            margin: 0;
            padding: 0; /* Changed to 0 to accommodate loading screen */
            background-color: #f4f7fe;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 50px;
        }
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }
        .block {
            background: linear-gradient(135deg, #ffffff 0%, #f5f7fa 100%);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        .block:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }

        /* Navigation button styles */
        .nav-button {
            background-color: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
            transition: background-color 0.3s ease;
        }

        .nav-button:hover {
            background-color: #2980b9;
            color: white;
        }
        .block h2 {
            margin: 20px 0 10px;
            color: #2c3e50;
            font-size: 1.5em;
        }
        .block-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }
        #manageRooms .block-icon { color: #3498db; }
        #manageProducts .block-icon { color: #2ecc71; }
        #lowStock .block-icon { color: #f39c12; }
        #saleHistory .block-icon { color: #e74c3c; }
        #expenses .block-icon { color: #9b59b6; }

        a{
            text-decoration: none;
        }

        /* Loading Screen Styles */
        #loadingScreen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.9);
            z-index: 9999;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        #loadingScreen .spinner {
            border: 8px solid #f3f3f3; /* Light grey */
            border-top: 8px solid #3498db; /* Blue */
            border-radius: 50%;
            width: 60px;
            height: 60px;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        #loadingScreen p {
            font-size: 18px;
            color: #333;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Mobile responsive styles */
        @media (max-width: 768px) {
            .container {
                padding: 20px 15px;
            }

            h1 {
                font-size: 2em;
                margin-bottom: 30px;
            }

            .dashboard {
                display: flex;
                flex-direction: column;
                gap: 20px;
                align-items: stretch;
            }

            .block {
                padding: 25px;
                width: 100%;
                box-sizing: border-box;
            }
        }

        /* Specific adjustments for small screens */
        @media (max-width: 650px) {
            .container {
                padding: 15px 10px;
            }

            /* Header button styling for mobile */
            .container > div:first-child {
                flex-direction: column !important;
                align-items: flex-start !important;
                gap: 15px;
                margin-bottom: 20px !important;
            }

            .container > div:first-child > div {
                display: flex !important;
                flex-direction: column;
                gap: 10px;
                width: 100%;
            }

            /* Navigation button mobile styling */
            .nav-button {
                width: 100% !important;
                padding: 12px 0 !important;
                text-align: center;
                font-size: 16px !important;
                box-sizing: border-box;
            }

            /* Logout button mobile styling */
            button {
                width: 100% !important;
                padding: 12px 0 !important;
                font-size: 16px !important;
                box-sizing: border-box;
            }

            h1 {
                font-size: 1.8em;
                margin-bottom: 20px;
            }

            .dashboard {
                display: flex;
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .block {
                padding: 20px;
                width: 100%;
                box-sizing: border-box;
            }

            .block h2 {
                font-size: 1.3em;
            }

            .block-icon {
                font-size: 2.5em;
            }
        }

        /* Specific adjustments for very small screens */
        @media (max-width: 480px) {
            .container {
                padding: 10px 5px;
            }

            /* Ensure header buttons remain properly styled on very small screens */
            .nav-button {
                font-size: 14px !important;
                padding: 10px 0 !important;
            }

            button {
                font-size: 14px !important;
                padding: 10px 0 !important;
            }

            h1 {
                font-size: 1.6em;
                margin-bottom: 15px;
            }

            .dashboard {
                display: flex;
                flex-direction: column;
                gap: 12px;
                align-items: stretch;
            }

            .block {
                padding: 15px;
                width: 100%;
                box-sizing: border-box;
            }

            .block h2 {
                font-size: 1.2em;
            }

            .block-icon {
                font-size: 2em;
            }
        }

    </style>
    <!-- Ensure you replace 'your-fontawesome-kit.js' with your actual Font Awesome Kit URL -->
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
</head>
<body>
    <!-- Loading Screen -->
    <div id="loadingScreen">
        <div class="spinner"></div>
        <p>Loading dashboard, please wait...</p>
    </div>

    <div class="container">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
            <h1 style="margin: 0;">Welcome, Admin</h1>
            <div style="display: flex; gap: 10px; align-items: center;">
                <a href="../index.html" class="nav-button">Go to User Page</a>
                <button onclick="logout()" style="background-color: #e74c3c; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">Logout</button>
            </div>
        </div>
        <div class="dashboard">
           <a href="./ManageRooms.html">
            <div class="block" id="manageRooms">
                <div class="block-icon"><i class="fas fa-door-open"></i></div>
                <h2>Manage Rooms</h2>
            </div>
           </a>

           <a href="./ManageProducts.html">
            <div class="block" id="manageProducts">
                <div class="block-icon"><i class="fas fa-box-open"></i></div>
                <h2>Manage Products</h2>
            </div>
           </a>


            <a href="./low-stock.html">
                <div class="block" id="lowStock">
                    <div class="block-icon"><i class="fas fa-exclamation-triangle"></i></div>
                    <h2>Low Stock</h2>
                </div>
            </a>

            <a href="./sales-history.html">
                <div class="block" id="saleHistory">
                    <div class="block-icon"><i class="fas fa-chart-line"></i></div>
                    <h2>Sale History</h2>
                </div>
            </a>

            <a href="./manage-users.html">
                <div class="block" id="saleHistory">
                    <div class="block-icon"><i class="fas fa-chart-line"></i></div>
                    <h2>Manage Users</h2>
                </div>
            </a>

            <a href="./backup-files.html">
                <div class="block" id="backupFiles">
                    <div class="block-icon"><i class="fas fa-cloud-download-alt"></i></div>
                    <h2>Backup Files</h2>
                </div>
            </a>

            <a href="./expenses.html">
                <div class="block" id="expenses">
                    <div class="block-icon"><i class="fas fa-receipt"></i></div>
                    <h2>Expenses</h2>
                </div>
            </a>
        </div>
    </div>

    <script>
        // Auto-detect environment and set appropriate API URL
        const API_BASE_URL = (() => {
          const hostname = window.location.hostname;

          // Production environment (Netlify)
          if (hostname === 'shans-system.netlify.app') {
            return 'https://shans-backend.onrender.com/api';
          }

          // Local development
          if (hostname === 'localhost' || hostname === '127.0.0.1') {
            return 'https://shans-backend.onrender.com/api';
          }

          // Default fallback to production
          return 'https://shans-backend.onrender.com/api';
        })();
        const loadingScreen = document.getElementById('loadingScreen'); // Loading Screen Element

        // Check authentication status
        async function checkAuthStatus() {
            const token = localStorage.getItem('authToken');
            const userInfo = localStorage.getItem('userInfo');

            if (!token || !userInfo) {
                // No token or user info, redirect to unified login
                window.location.href = '../login.html';
                return false;
            }

            const user = JSON.parse(userInfo);
            if (!user.is_admin) {
                // User is not admin, redirect to unified login
                localStorage.removeItem('authToken');
                localStorage.removeItem('userInfo');
                window.location.href = '../login.html';
                return false;
            }

            try {
                // Verify token with backend
                const response = await fetch(`${API_BASE_URL}/auth/me`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (!response.ok) {
                    // Token is invalid, redirect to login
                    localStorage.removeItem('authToken');
                    localStorage.removeItem('userInfo');
                    window.location.href = '../login.html';
                    return false;
                }

                const data = await response.json();
                if (!data.user.is_admin) {
                    // User is not admin, redirect to login
                    localStorage.removeItem('authToken');
                    localStorage.removeItem('userInfo');
                    window.location.href = '../login.html';
                    return false;
                }

                return true;
            } catch (error) {
                console.error('Auth check failed:', error);
                localStorage.removeItem('authToken');
                localStorage.removeItem('userInfo');
                window.location.href = '../login.html';
                return false;
            }
        }

        // Add logout functionality
        function logout() {
            fetch(`${API_BASE_URL}/auth/logout`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                }
            })
            .then(() => {
                localStorage.removeItem('authToken');
                localStorage.removeItem('userInfo');
                window.location.href = '../login.html';
            })
            .catch(error => {
                console.error('Logout error:', error);
                // Even if logout fails on server, clear local storage and redirect
                localStorage.removeItem('authToken');
                localStorage.removeItem('userInfo');
                window.location.href = '../login.html';
            });
        }

        window.onload = async function() {
            // Check authentication first
            const isAuthenticated = await checkAuthStatus();
            if (!isAuthenticated) {
                return;
            }

            // Simulate data fetching or any other initialization tasks
            // If you have actual data fetching, place it here and hide the loading screen after it's done

            // For demonstration, we'll hide the loading screen after the page has fully loaded
            loadingScreen.style.display = 'none';
        };
    </script>
</body>
</html>
